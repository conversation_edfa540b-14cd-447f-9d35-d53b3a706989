<template>
  <el-dialog
    v-model="visible"
    title="水龄分级配置"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="grade-config-container">
      <div class="config-header">
        <el-button type="primary" @click="addGrade">
          <el-icon><Plus /></el-icon>
          添加分级
        </el-button>
        <el-button @click="resetToDefault">重置为默认</el-button>
      </div>

      <el-table :data="gradeConfigs" border style="width: 100%">
        <el-table-column prop="gradeName" label="分级名称" width="120">
          <template #default="{ row, $index }">
            <el-input
              v-model="row.gradeName"
              placeholder="请输入分级名称"
              @change="validateGrade($index)"
            />
          </template>
        </el-table-column>

        <el-table-column label="水龄范围(小时)" width="200">
          <template #default="{ row, $index }">
            <div class="age-range-input">
              <el-input-number
                v-model="row.minAge"
                :min="0"
                :max="999"
                :precision="1"
                size="small"
                @change="validateGrade($index)"
              />
              <span class="range-separator">-</span>
              <el-input-number
                v-model="row.maxAge"
                :min="0"
                :max="999"
                :precision="1"
                size="small"
                @change="validateGrade($index)"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="color" label="颜色" width="100">
          <template #default="{ row }">
            <el-color-picker v-model="row.color" />
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="150">
          <template #default="{ row }">
            <el-input
              v-model="row.description"
              placeholder="请输入描述"
              type="textarea"
              :rows="1"
            />
          </template>
        </el-table-column>

        <el-table-column label="异常标记" width="100">
          <template #default="{ row }">
            <el-switch v-model="row.abnormal" />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="80">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              @click="removeGrade($index)"
              :disabled="gradeConfigs.length <= 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="validation-info" v-if="validationErrors.length > 0">
        <el-alert
          title="配置验证错误"
          type="error"
          :closable="false"
          show-icon
        >
          <ul>
            <li v-for="error in validationErrors" :key="error">{{ error }}</li>
          </ul>
        </el-alert>
      </div>

      <div class="preview-section">
        <h4>预览效果</h4>
        <div class="grade-preview">
          <div
            v-for="(grade, index) in gradeConfigs"
            :key="index"
            class="grade-item"
          >
            <div
              class="color-indicator"
              :style="{ backgroundColor: grade.color }"
            ></div>
            <span class="grade-name">{{ grade.gradeName }}</span>
            <span class="age-range">{{ grade.minAge }}-{{ grade.maxAge }}小时</span>
            <span v-if="grade.abnormal" class="abnormal-tag">异常</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :disabled="!isValid">
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { setWaterAge, getDefaultWaterAge } from '@/api/waterModels/waterAge';

interface GradeConfig {
  gradeName: string;
  minAge: number;
  maxAge: number;
  color: string;
  description: string;
  abnormal: boolean;
}

const props = defineProps<{
  modelValue: boolean;
  initialConfigs?: GradeConfig[];
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'save': [configs: GradeConfig[]];
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const gradeConfigs = ref<GradeConfig[]>([]);
const validationErrors = ref<string[]>([]);

// 默认配置
const defaultConfigs: GradeConfig[] = [
  {
    gradeName: '优秀',
    minAge: 0,
    maxAge: 12,
    color: '#00FF00',
    description: '水质优秀，流动性好',
    abnormal: false,
  },
  {
    gradeName: '良好',
    minAge: 12,
    maxAge: 24,
    color: '#90EE90',
    description: '水质良好，正常范围',
    abnormal: false,
  },
  {
    gradeName: '一般',
    minAge: 24,
    maxAge: 48,
    color: '#FFD700',
    description: '水质一般，需要关注',
    abnormal: false,
  },
  {
    gradeName: '较差',
    minAge: 48,
    maxAge: 72,
    color: '#FFA500',
    description: '水质较差，需要处理',
    abnormal: true,
  },
  {
    gradeName: '异常',
    minAge: 72,
    maxAge: 999,
    color: '#FF0000',
    description: '水质异常，立即处理',
    abnormal: true,
  },
];

// 初始化配置
const initConfigs = () => {
  if (props.initialConfigs && props.initialConfigs.length > 0) {
    gradeConfigs.value = JSON.parse(JSON.stringify(props.initialConfigs));
  } else {
    gradeConfigs.value = JSON.parse(JSON.stringify(defaultConfigs));
  }
};

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    initConfigs();
    validateAllGrades();
  }
});

// 验证配置
const validateGrade = (index: number) => {
  setTimeout(() => {
    validateAllGrades();
  }, 100);
};

const validateAllGrades = () => {
  validationErrors.value = [];
  
  // 检查基本字段
  gradeConfigs.value.forEach((grade, index) => {
    if (!grade.gradeName.trim()) {
      validationErrors.value.push(`第${index + 1}行：分级名称不能为空`);
    }
    
    if (grade.minAge >= grade.maxAge) {
      validationErrors.value.push(`第${index + 1}行：最小水龄必须小于最大水龄`);
    }
    
    if (grade.minAge < 0 || grade.maxAge < 0) {
      validationErrors.value.push(`第${index + 1}行：水龄值不能为负数`);
    }
  });
  
  // 检查重复名称
  const names = gradeConfigs.value.map(g => g.gradeName.trim());
  const duplicateNames = names.filter((name, index) => names.indexOf(name) !== index);
  if (duplicateNames.length > 0) {
    validationErrors.value.push(`分级名称重复：${[...new Set(duplicateNames)].join(', ')}`);
  }
  
  // 检查范围重叠
  const sortedGrades = [...gradeConfigs.value].sort((a, b) => a.minAge - b.minAge);
  for (let i = 0; i < sortedGrades.length - 1; i++) {
    if (sortedGrades[i].maxAge > sortedGrades[i + 1].minAge) {
      validationErrors.value.push(`水龄范围重叠：${sortedGrades[i].gradeName} 和 ${sortedGrades[i + 1].gradeName}`);
    }
  }
};

const isValid = computed(() => validationErrors.value.length === 0);

// 添加分级
const addGrade = () => {
  const lastGrade = gradeConfigs.value[gradeConfigs.value.length - 1];
  const newGrade: GradeConfig = {
    gradeName: `分级${gradeConfigs.value.length + 1}`,
    minAge: lastGrade ? lastGrade.maxAge : 0,
    maxAge: lastGrade ? lastGrade.maxAge + 24 : 24,
    color: '#999999',
    description: '',
    abnormal: false,
  };
  
  gradeConfigs.value.push(newGrade);
  validateAllGrades();
};

// 删除分级
const removeGrade = (index: number) => {
  if (gradeConfigs.value.length > 1) {
    gradeConfigs.value.splice(index, 1);
    validateAllGrades();
  }
};

// 重置为默认
const resetToDefault = () => {
  gradeConfigs.value = JSON.parse(JSON.stringify(defaultConfigs));
  validateAllGrades();
};

// 保存配置
const handleSave = async () => {
  if (!isValid.value) {
    ElMessage.error('请修正配置错误后再保存');
    return;
  }
  
  try {
    const result = await setWaterAge(gradeConfigs.value);
    if (result.data?.success) {
      ElMessage.success('配置保存成功');
      emit('save', gradeConfigs.value);
      handleClose();
    } else {
      ElMessage.error(result.data?.message || '保存失败');
    }
  } catch (error) {
    console.error('保存配置失败:', error);
    ElMessage.error('保存失败，请重试');
  }
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
};
</script>

<style lang="scss" scoped>
.grade-config-container {
  .config-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .age-range-input {
    display: flex;
    align-items: center;
    gap: 8px;

    .range-separator {
      color: #666;
      font-weight: 500;
    }
  }

  .validation-info {
    margin: 16px 0;

    ul {
      margin: 8px 0 0 0;
      padding-left: 20px;
    }
  }

  .preview-section {
    margin-top: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
    }

    .grade-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .grade-item {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      background-color: #ffffff;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      font-size: 12px;

      .color-indicator {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        margin-right: 8px;
        border: 1px solid #ccc;
      }

      .grade-name {
        font-weight: 500;
        margin-right: 8px;
      }

      .age-range {
        color: #666;
        margin-right: 8px;
      }

      .abnormal-tag {
        background-color: #fee2e2;
        color: #dc2626;
        padding: 2px 6px;
        border-radius: 2px;
        font-size: 10px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
