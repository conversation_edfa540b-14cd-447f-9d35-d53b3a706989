<!-- 泵房台账 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="FormConfig"></DialogForm>
    <DialogForm ref="refUploadDialog" :config="uploadConfig" class="upload-dialog">
      <el-form ref="formRef">
        <el-form-item>
          <div class="buttons">
            <el-button type="primary" :icon="Download" :plain="true" @click="downloadTemplate">
              下载模板
            </el-button>
            <el-upload ref="upload" action="action"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" :show-file-list="true"
              :on-remove="handleRemove" :limit="1" :auto-upload="false" :on-change="clickUpload" class="upload-demo">
              <el-button type="primary" :icon="Upload"> 添加文件 </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能导入xlsx文件, 请确保导入的文件单元格格式为文本!
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <FormTable :config="uploadTableConfig"></FormTable>
    </DialogForm>
    <DialogForm ref="refFileUpload" :config="uploadFormConfig">
      <Search ref="uploadSearch" :config="uploadSearchConfig" />
      <div class="btns">
        <el-upload ref="uploadRef" :action="state.fileActionUrl" :show-file-list="true"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" :auto-upload="false" multiple
          :headers="{
            'X-Authorization': 'Bearer ' + useUserStore().token,
          }" :on-success="(res, file) => clickUploadFile(res, file)" class="upload-demo">
          <template #trigger>
            <el-button type="primary"> 读取文件 </el-button>
          </template>
        </el-upload>

        <el-button type="success" @click="() => uploadRef?.submit()"> 上传 </el-button>
      </div>
      <FormTable :config="fileTableConfig" class="form-table" />
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
import {
  Delete,
  Download,
  Edit,
  Plus,
  Refresh,
  Search as SearchIcon,
  Upload,
} from '@element-plus/icons-vue';
import { shallowRef } from 'vue';
import { ElMessage } from 'element-plus';
import type { UploadInstance } from 'element-plus';
import { SLConfirm } from '@/utils/Message.js';
import { downloadFile } from '@/utils/fileHelper';
import {
  accountColumns,
  formDataFields,
  moreFormDataFields,
  readExcelToJson,
  pumpRoomUpload,
} from './data';
import {
  addPumpHouseStorage,
  delPumpHouseStorage,
  editPumpHouseStorage,
  pumpHouseStorageList,
  batchAddPumpHouseStorage,
  pumpHouseStorageTemplate,
  pumpHouseStorageExport,
  fileRegistryList,
  saveRegistry,
  delRegistry,
} from '@/api/secondSupplyManage/pumpRoomInfo';
import { useUserStore } from '@/store';
import useGlobal from '@/hooks/global/useGlobal';

const { $messageSuccess, $messageError } = useGlobal();

const state = reactive<{
  dataList: any;
  fileType: string;
  fileActionUrl: string;
  fileList: any;
  rowId: string;
}>({
  dataList: [],
  fileType: '',
  fileActionUrl: '/file/api/upload/file',
  fileList: [],
  rowId: '',
});
const refForm = ref<IDialogFormIns>();
const refTable = ref<ICardTableIns>();
const refUploadDialog = ref<IDialogFormIns>();
const refFileUpload = ref<IDialogFormIns>();
const refSearch = ref<ISearchIns>();
const uploadSearch = ref<ISearchIns>();
const uploadRef = ref<UploadInstance>();
// 搜索配置
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '泵房编码',
      field: 'code',
      placeholder: '请输入泵房编码',
    },
    {
      type: 'input',
      label: '泵房名称',
      field: 'name',
      placeholder: '请输入泵房名称',
    },
    {
      type: 'input',
      label: '泵房简称',
      field: 'nickname',
      placeholder: '请输入泵房简称',
    },
    ...moreFormDataFields,
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData(),
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
          },
        },
        {
          perm: true,
          text: '添加',
          type: 'success',
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.title = '新增';
            handleEdit();
          },
        },
        {
          perm: true,
          text: '导入',
          type: 'warning',
          svgIcon: shallowRef(Upload),
          click: () => {
            handleRemove();
            refUploadDialog.value?.openDialog();
          },
        },
        {
          perm: true,
          text: '导出',
          type: 'danger',
          svgIcon: shallowRef(Download),
          click: () => exportTable(),
        },
      ],
    },
  ],
});
// 列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: accountColumns,
  operations: [
    {
      perm: true,
      text: '修改',
      svgIcon: shallowRef(Edit),
      click: row => {
        FormConfig.title = '修改';
        handleEdit(row);
      },
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: row => handleDelete(row),
    },
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    },
  },
});
// 表单配置
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 600,
  title: '新增',
  labelWidth: 120,
  group: [{ fields: formDataFields() }],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(() => {
        if (FormConfig.title === '新增') {
          addPumpHouseStorage(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('提交成功');
            refreshData();
          });
        } else {
          editPumpHouseStorage(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('修改成功');
            refreshData();
          });
        }
      })
      .catch(() => {
        //
      });
  },
});

// 导入弹框
const uploadConfig = reactive<IDialogFormConfig>({
  title: '导入',
  dialogWidth: 1200,
  group: [],
  cancel: true,
  btns: [
    {
      perm: true,
      text: '确定导入',
      click: () => {
        if (state.dataList.length > 0) {
          console.log(state.dataList);
          batchAddPumpHouseStorage(state.dataList)
            .then(res => {
              console.log(res.data);
              if (res.data?.code === 200) {
                handleRemove();
                refUploadDialog.value?.closeDialog();
                ElMessage.success('提交成功');
                refreshData();
              } else {
                ElMessage.error('提交失败');
              }
            })
            .catch(error => {
              console.log(error);
              ElMessage.error('提交失败');
            });
        } else {
          ElMessage.warning('请导入正确的xlsx文件！');
        }
      },
    },
  ],
});

// 导出列表
const exportTable = async () => {
  const query = refSearch.value?.queryParams || {};
  const [installDateFrom, installDateTo] = query.installDateFrom;
  const [fromTime, toTime] = query.fromTime || [];
  const params = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: -1,
    installDateFrom: installDateFrom ? dayjs(installDateFrom).startOf('day').valueOf() : null,
    installDateTo: installDateTo ? dayjs(installDateTo).startOf('day').valueOf() : null,
    fromTime,
    toTime,
  };
  const res = await pumpHouseStorageExport(params);
  const url = window.URL.createObjectURL(res.data);
  console.log(url);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', `泵房台账列表.xlsx`);
  document.body.appendChild(link);
  link.click();
};
// 下载模板
const downloadTemplate = async () => {
  const res = await pumpHouseStorageTemplate();
  const url = window.URL.createObjectURL(res.data);
  // const binaryData:any = []
  // binaryData.push(res.data)
  // const url = window.URL.createObjectURL(new Blob(binaryData))
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', `泵房台账模板.xlsx`);
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(link.href);
};

//
const uploadTableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: formDataFields(),
  dataList: [],
  pagination: {
    hide: true,
  },
});

// 附件上传
const uploadFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 1050,
  title: '上传文件',
  labelWidth: 120,
  group: [{ fields: formDataFields() }],
});
// 附件上传列表
const fileTableConfig = reactive<ITable>({
  indexVisible: true,
  columns: [
    { label: '文件', prop: 'fileName', minWidth: 120 },
    {
      label: '上传时间',
      prop: 'uploadTime',
      minWidth: 120,
      formatter: (row: any, value: any) => {
        return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      text: '下载',
      type: 'primary',
      svgIcon: shallowRef(Download),
      click: row => {
        downloadFile(row.fileAddress, row.fileName);
      },
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: row => {
        SLConfirm('确定删除该附件, 是否继续?', '删除提示').then(() => {
          delRegistry(row.id)
            .then(res => {
              if (res.data?.code === 200) {
                $messageSuccess('删除成功');
              } else {
                $messageError('删除失败');
              }
              refreshFileData(state.fileType, state.rowId);
            })
            .catch(err => {
              $messageError(err);
            });
        });
      },
    },
  ],
  pagination: {
    hide: true,
  },
});
//  附件查询配置
const uploadSearchConfig = reactive<ISearch>({
  defaultParams: {
    time: [],
  },
  filters: [
    {
      type: 'input',
      label: '文件名',
      field: 'fileName',
      placeholder: '请输入文件名',
    },
    {
      type: 'daterange',
      label: '上传日期',
      field: 'fromTime',
      format: 'YYYY-MM-DD',
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshFileData(state.fileType, state.rowId),
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            uploadSearch.value?.resetForm();
          },
        },
      ],
    },
  ],
});
const handleRemove = () => {
  state.dataList = [];
  uploadTableConfig.dataList = [];
};
// 解析导入文件数据
const clickUpload = (file: any) => {
  state.dataList = [];
  readExcelToJson(file).then((res: any) => {
    res &&
      res.forEach(el => {
        const val = {};
        for (const i in el) {
          if (typeof pumpRoomUpload[i] === 'undefined') {
            ElMessage.info(
              '请检查第一行表头是否为: 泵房编码/泵房名称/泵房简称/厂家名称/供水类型/水箱个数/地址/安装人/安装日期/采集频率/储存频率; 且每行均有对应数据!',
            );
            return;
          }
          val[pumpRoomUpload[i]] = el[i];
        }
        state.dataList.push(val);
      });
    const strings = state.dataList.map(item => JSON.stringify(item));
    const removeDupList = [...new Set(strings)]; // 也可以使用Array.from(new Set(strings))
    uploadTableConfig.dataList = removeDupList.map((item: any) => JSON.parse(item));
  });
};

// 编辑弹框
const handleEdit = (row?: any) => {
  FormConfig.defaultValue = row
    ? {
      ...row,
      installDate: dayjs(row.installDate).format(),
    }
    : {};
  refForm.value?.openDialog();
};
// 删除数据
const handleDelete = (row?: any) => {
  SLConfirm('确定删除?', '提示信息')
    .then(() => {
      delPumpHouseStorage(row.id).then(() => {
        refreshData();
      });
    })
    .catch(() => {
      //
    });
};

const clickUploadFile = async (res, file) => {
  const param = {
    fileName: file.name,
    fileAddress: file.response,
    label: state.fileType,
    host: state.rowId,
  };
  saveRegistry(param).then(() => {
    refreshFileData(state.fileType, state.rowId);
  });
};

const refreshFileData = async (label: string, id: string) => {
  const query = uploadSearch.value?.queryParams || {
    fromTime: [],
  };
  const [start, end] = query.fromTime || [];
  const newParams = {
    ...query,
    host: id,
    label,
    fromTime: start,
    toTime: end,
    page: 1,
    size: 99999,
  };
  state.rowId = id;
  const res = await fileRegistryList(newParams);
  console.log(res.data?.data?.data);
  fileTableConfig.dataList = res.data?.data?.data;
};
// 刷新数据
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {};
  const [installDateFrom, installDateTo] = query.installDateFrom;
  const [fromTime, toTime] = query.fromTime;
  const params = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    installDateFrom: installDateFrom ? dayjs(installDateFrom).startOf('day').valueOf() : null,
    installDateTo: installDateTo ? dayjs(installDateTo).startOf('day').valueOf() : null,
    fromTime,
    toTime,
  };
  const result = await pumpHouseStorageList(params);
  TableConfig.pagination.total = result.data?.data.total;
  TableConfig.dataList = result.data?.data.data;
};

onMounted(async () => {
  await refreshData();
});
</script>
<style lang="scss" scoped>
.upload-demo {}

.buttons {
  display: flex;
  align-items: flex-start;

  button {
    margin-right: 10px;
  }
}

.btns {
  display: flex;

  .el-button {
    margin-right: 20px;
  }
}

.form-table {
  height: 40vh;
}
</style>
