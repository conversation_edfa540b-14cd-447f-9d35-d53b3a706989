import request from '@/plugins/axios';

// ==================== 关阀分析台账管理接口 ====================

/**
 * 关阀分析台账数据结构
 */
export interface CloseValveAccount {
  id?: string;
  accountCode: string;
  accountName: string;
  analysisDate: string;
  burstLocation: {
    x: number;
    y: number;
    address: string;
    district?: string;
  };
  analysisType: 'emergency' | 'planned' | 'maintenance';
  priority: 'high' | 'medium' | 'low';
  status: 'draft' | 'analyzing' | 'completed' | 'archived';
  valveCount: number;
  pipeLength: number;
  affectedUsers: number;
  estimatedDuration: number; // 预计停水时长（小时）
  actualDuration?: number; // 实际停水时长（小时）
  operator: string;
  reviewer?: string;
  approver?: string;
  createTime: string;
  updateTime?: string;
  completeTime?: string;
  remark?: string;
  attachments?: Array<{
    id: string;
    fileName: string;
    fileUrl: string;
    fileType: string;
    uploadTime: string;
  }>;
}

/**
 * 关阀分析台账查询参数
 */
export interface CloseValveAccountQuery {
  page?: number;
  size?: number;
  accountCode?: string;
  accountName?: string;
  analysisType?: string;
  priority?: string;
  status?: string;
  operator?: string;
  district?: string;
  startDate?: string;
  endDate?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

/**
 * 获取关阀分析台账列表
 * @param params 查询参数
 */
export function getCloseValveAccountList(params: CloseValveAccountQuery) {
  return request({
    url: '/waterModels/valve/account/list',
    method: 'get',
    params,
  });
}

/**
 * 根据ID获取关阀分析台账详情
 * @param id 台账ID
 */
export function getCloseValveAccountById(id: string) {
  return request({
    url: `/waterModels/valve/account/detail/${id}`,
    method: 'get',
  });
}

/**
 * 新增关阀分析台账
 * @param account 台账数据
 */
export function addCloseValveAccount(account: CloseValveAccount) {
  return request({
    url: '/waterModels/valve/account/add',
    method: 'post',
    data: account,
  });
}

/**
 * 更新关阀分析台账
 * @param account 台账数据
 */
export function updateCloseValveAccount(account: CloseValveAccount) {
  return request({
    url: `/waterModels/valve/account/update/${account.id}`,
    method: 'put',
    data: account,
  });
}

/**
 * 删除关阀分析台账
 * @param id 台账ID
 */
export function deleteCloseValveAccount(id: string) {
  return request({
    url: `/waterModels/valve/account/delete/${id}`,
    method: 'delete',
  });
}

/**
 * 批量删除关阀分析台账
 * @param ids 台账ID数组
 */
export function batchDeleteCloseValveAccounts(ids: string[]) {
  return request({
    url: '/waterModels/valve/account/batch-delete',
    method: 'delete',
    data: { ids },
  });
}

/**
 * 提交关阀分析台账审核
 * @param id 台账ID
 * @param reviewData 审核数据
 */
export function submitCloseValveAccountReview(
  id: string,
  reviewData: {
    reviewComment?: string;
    attachments?: string[];
  }
) {
  return request({
    url: `/waterModels/valve/account/submit-review/${id}`,
    method: 'post',
    data: reviewData,
  });
}

/**
 * 审核关阀分析台账
 * @param id 台账ID
 * @param reviewResult 审核结果
 */
export function reviewCloseValveAccount(
  id: string,
  reviewResult: {
    approved: boolean;
    reviewComment: string;
    reviewer: string;
    reviewTime: string;
  }
) {
  return request({
    url: `/waterModels/valve/account/review/${id}`,
    method: 'post',
    data: reviewResult,
  });
}

/**
 * 批准关阀分析台账
 * @param id 台账ID
 * @param approvalData 批准数据
 */
export function approveCloseValveAccount(
  id: string,
  approvalData: {
    approved: boolean;
    approvalComment: string;
    approver: string;
    approvalTime: string;
  }
) {
  return request({
    url: `/waterModels/valve/account/approve/${id}`,
    method: 'post',
    data: approvalData,
  });
}

/**
 * 归档关阀分析台账
 * @param id 台账ID
 */
export function archiveCloseValveAccount(id: string) {
  return request({
    url: `/waterModels/valve/account/archive/${id}`,
    method: 'post',
  });
}

/**
 * 批量归档关阀分析台账
 * @param ids 台账ID数组
 */
export function batchArchiveCloseValveAccounts(ids: string[]) {
  return request({
    url: '/waterModels/valve/account/batch-archive',
    method: 'post',
    data: { ids },
  });
}

/**
 * 导出关阀分析台账
 * @param params 导出参数
 */
export function exportCloseValveAccounts(params: {
  ids?: string[];
  query?: CloseValveAccountQuery;
  format?: 'excel' | 'pdf';
  includeDetails?: boolean;
}) {
  return request({
    url: '/waterModels/valve/account/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

/**
 * 获取关阀分析台账统计数据
 * @param params 统计参数
 */
export function getCloseValveAccountStatistics(params?: {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  analysisType?: string;
  district?: string;
}) {
  return request({
    url: '/waterModels/valve/account/statistics',
    method: 'get',
    params,
  });
}

/**
 * 获取关阀分析台账模板
 */
export function getCloseValveAccountTemplate() {
  return request({
    url: '/waterModels/valve/account/template',
    method: 'get',
    responseType: 'blob',
  });
}

/**
 * 批量导入关阀分析台账
 * @param file 导入文件
 */
export function importCloseValveAccounts(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/waterModels/valve/account/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取关阀分析台账操作日志
 * @param accountId 台账ID
 */
export function getCloseValveAccountLogs(accountId: string) {
  return request({
    url: `/waterModels/valve/account/logs/${accountId}`,
    method: 'get',
  });
}

/**
 * 复制关阀分析台账
 * @param id 源台账ID
 * @param newAccountData 新台账数据
 */
export function copyCloseValveAccount(
  id: string,
  newAccountData: {
    accountCode: string;
    accountName: string;
    analysisDate: string;
  }
) {
  return request({
    url: `/waterModels/valve/account/copy/${id}`,
    method: 'post',
    data: newAccountData,
  });
}

/**
 * 获取关阀分析台账附件
 * @param accountId 台账ID
 */
export function getCloseValveAccountAttachments(accountId: string) {
  return request({
    url: `/waterModels/valve/account/attachments/${accountId}`,
    method: 'get',
  });
}

/**
 * 上传关阀分析台账附件
 * @param accountId 台账ID
 * @param file 附件文件
 */
export function uploadCloseValveAccountAttachment(accountId: string, file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: `/waterModels/valve/account/upload-attachment/${accountId}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 删除关阀分析台账附件
 * @param accountId 台账ID
 * @param attachmentId 附件ID
 */
export function deleteCloseValveAccountAttachment(accountId: string, attachmentId: string) {
  return request({
    url: `/waterModels/valve/account/delete-attachment/${accountId}/${attachmentId}`,
    method: 'delete',
  });
}
