<!-- 关阀方案报告生成器 -->
<template>
  <div class="report-generator">
    <el-dialog
      v-model="visible"
      title="生成关阀分析报告"
      width="60%"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-form :model="reportConfig" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告名称" prop="reportName">
              <el-input v-model="reportConfig.reportName" placeholder="请输入报告名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告格式" prop="format">
              <el-select v-model="reportConfig.format" placeholder="请选择报告格式">
                <el-option label="PDF格式" value="PDF" />
                <el-option label="Excel格式" value="EXCEL" />
                <el-option label="Word格式" value="WORD" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告类型" prop="reportType">
              <el-select v-model="reportConfig.reportType" placeholder="请选择报告类型">
                <el-option label="详细报告" value="detailed" />
                <el-option label="简要报告" value="summary" />
                <el-option label="统计报告" value="statistics" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告语言" prop="language">
              <el-select v-model="reportConfig.language" placeholder="请选择报告语言">
                <el-option label="中文" value="zh-CN" />
                <el-option label="英文" value="en-US" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="包含内容">
          <el-checkbox-group v-model="reportConfig.includeContent">
            <el-checkbox label="basicInfo">基本信息</el-checkbox>
            <el-checkbox label="analysisResults">分析结果</el-checkbox>
            <el-checkbox label="valveDetails">阀门详情</el-checkbox>
            <el-checkbox label="pipeDetails">管线详情</el-checkbox>
            <el-checkbox label="impactAnalysis">影响分析</el-checkbox>
            <el-checkbox label="mapImages">地图图片</el-checkbox>
            <el-checkbox label="statistics">统计图表</el-checkbox>
            <el-checkbox label="recommendations">建议措施</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="地图设置" v-if="reportConfig.includeContent.includes('mapImages')">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="地图尺寸" label-width="80px">
                <el-select v-model="reportConfig.mapSettings.size" placeholder="选择尺寸">
                  <el-option label="小 (800x600)" value="small" />
                  <el-option label="中 (1200x900)" value="medium" />
                  <el-option label="大 (1600x1200)" value="large" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="图片格式" label-width="80px">
                <el-select v-model="reportConfig.mapSettings.format" placeholder="选择格式">
                  <el-option label="PNG" value="png" />
                  <el-option label="JPG" value="jpg" />
                  <el-option label="SVG" value="svg" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="DPI" label-width="80px">
                <el-select v-model="reportConfig.mapSettings.dpi" placeholder="选择DPI">
                  <el-option label="72 DPI" :value="72" />
                  <el-option label="150 DPI" :value="150" />
                  <el-option label="300 DPI" :value="300" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-checkbox v-model="reportConfig.mapSettings.includeOverview">包含总览图</el-checkbox>
          <el-checkbox v-model="reportConfig.mapSettings.includeDetails">包含详细图</el-checkbox>
        </el-form-item>

        <el-form-item label="统计设置" v-if="reportConfig.includeContent.includes('statistics')">
          <el-checkbox-group v-model="reportConfig.statisticsSettings.charts">
            <el-checkbox label="valveChart">阀门统计图</el-checkbox>
            <el-checkbox label="pipeChart">管线统计图</el-checkbox>
            <el-checkbox label="impactChart">影响范围图</el-checkbox>
            <el-checkbox label="timeChart">时间分析图</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="备注信息">
          <el-input
            v-model="reportConfig.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入报告备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handlePreview" :loading="previewLoading">预览报告</el-button>
          <el-button type="success" @click="handleGenerate" :loading="generateLoading">生成报告</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 报告预览弹框 -->
    <el-dialog
      v-model="previewVisible"
      title="报告预览"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="report-preview" v-loading="previewLoading">
        <div v-if="previewData" class="preview-content">
          <div class="report-header">
            <h1>{{ previewData.title }}</h1>
            <div class="report-meta">
              <p>生成时间: {{ previewData.generateTime }}</p>
              <p>报告类型: {{ getReportTypeLabel(reportConfig.reportType) }}</p>
              <p>分析方案: {{ previewData.schemeName }}</p>
            </div>
          </div>

          <div class="report-section" v-if="reportConfig.includeContent.includes('basicInfo')">
            <h2>基本信息</h2>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="爆管位置">{{ previewData.burstLocation }}</el-descriptions-item>
              <el-descriptions-item label="分析时间">{{ previewData.analysisTime }}</el-descriptions-item>
              <el-descriptions-item label="分析类型">{{ previewData.analysisType }}</el-descriptions-item>
              <el-descriptions-item label="优先级">{{ previewData.priority }}</el-descriptions-item>
              <el-descriptions-item label="影响阀门数">{{ previewData.valveCount }}个</el-descriptions-item>
              <el-descriptions-item label="影响用户数">{{ previewData.affectedUsers }}户</el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="report-section" v-if="reportConfig.includeContent.includes('analysisResults')">
            <h2>分析结果</h2>
            <el-tabs>
              <el-tab-pane label="初始关闭阀门">
                <el-table :data="previewData.initialValves" size="small" max-height="300">
                  <el-table-column prop="id" label="阀门ID" />
                  <el-table-column prop="name" label="阀门名称" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="status" label="状态" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="二次关闭阀门">
                <el-table :data="previewData.secondaryValves" size="small" max-height="300">
                  <el-table-column prop="id" label="阀门ID" />
                  <el-table-column prop="name" label="阀门名称" />
                  <el-table-column prop="type" label="类型" />
                  <el-table-column prop="status" label="状态" />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div class="report-section" v-if="reportConfig.includeContent.includes('mapImages')">
            <h2>地图图片</h2>
            <div class="map-images">
              <div class="map-image-item">
                <h3>总览图</h3>
                <div class="image-placeholder">地图图片将在正式报告中生成</div>
              </div>
              <div class="map-image-item">
                <h3>详细图</h3>
                <div class="image-placeholder">地图图片将在正式报告中生成</div>
              </div>
            </div>
          </div>

          <div class="report-section" v-if="reportConfig.includeContent.includes('statistics')">
            <h2>统计分析</h2>
            <div class="statistics-charts">
              <div class="chart-placeholder">统计图表将在正式报告中生成</div>
            </div>
          </div>

          <div class="report-section" v-if="reportConfig.includeContent.includes('recommendations')">
            <h2>建议措施</h2>
            <ul>
              <li>立即关闭初始阀门，隔离故障区域</li>
              <li>通知受影响用户，做好停水准备</li>
              <li>派遣维修队伍到现场进行抢修</li>
              <li>监控二次关闭阀门状态，必要时执行二次关闭</li>
              <li>建立应急供水方案，保障基本用水需求</li>
            </ul>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewVisible = false">关闭预览</el-button>
          <el-button type="success" @click="handleGenerate" :loading="generateLoading">生成报告</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import dayjs from 'dayjs';
import {
  generateCloseValveReport,
  exportCloseValveReport,
  type CloseValveScheme,
  type ReportConfig,
} from '@/api/waterModels/closeValveAnalysis';

// Props
interface Props {
  scheme?: CloseValveScheme | null;
}

const props = withDefaults(defineProps<Props>(), {
  scheme: null,
});

// 响应式数据
const visible = ref(false);
const previewVisible = ref(false);
const previewLoading = ref(false);
const generateLoading = ref(false);
const formRef = ref<FormInstance>();
const previewData = ref<any>(null);

const reportConfig = reactive<ReportConfig>({
  reportName: '',
  format: 'PDF',
  reportType: 'detailed',
  language: 'zh-CN',
  includeContent: ['basicInfo', 'analysisResults', 'mapImages'],
  mapSettings: {
    size: 'medium',
    format: 'png',
    dpi: 150,
    includeOverview: true,
    includeDetails: true,
  },
  statisticsSettings: {
    charts: ['valveChart', 'pipeChart', 'impactChart'],
  },
  remark: '',
});

// 表单验证规则
const rules: FormRules = {
  reportName: [
    { required: true, message: '请输入报告名称', trigger: 'blur' },
    { min: 2, max: 50, message: '报告名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  format: [
    { required: true, message: '请选择报告格式', trigger: 'change' },
  ],
  reportType: [
    { required: true, message: '请选择报告类型', trigger: 'change' },
  ],
};

// 工具函数
const getReportTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    detailed: '详细报告',
    summary: '简要报告',
    statistics: '统计报告',
  };
  return map[type] || type;
};

// 监听方案变化，自动设置报告名称
watch(() => props.scheme, (newScheme) => {
  if (newScheme) {
    reportConfig.reportName = `${newScheme.schemeName}_报告_${dayjs().format('YYYYMMDD_HHmmss')}`;
  }
}, { immediate: true });

// 事件处理
const show = () => {
  visible.value = true;
};

const handleClose = () => {
  visible.value = false;
  previewVisible.value = false;
  formRef.value?.resetFields();
};

const handlePreview = async () => {
  if (!props.scheme) {
    ElMessage.error('没有可预览的方案数据');
    return;
  }

  try {
    await formRef.value?.validate();
    previewLoading.value = true;
    
    // 模拟生成预览数据
    previewData.value = {
      title: reportConfig.reportName,
      generateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      schemeName: props.scheme.schemeName,
      burstLocation: props.scheme.burstLocation?.address || '未知位置',
      analysisTime: props.scheme.analysisDate,
      analysisType: props.scheme.analysisType,
      priority: props.scheme.priority,
      valveCount: props.scheme.valveCount,
      affectedUsers: props.scheme.affectedUsers,
      initialValves: props.scheme.analysisResults?.initialValves || [],
      secondaryValves: props.scheme.analysisResults?.secondaryValves || [],
    };
    
    previewVisible.value = true;
  } catch (error) {
    console.error('预览失败:', error);
  } finally {
    previewLoading.value = false;
  }
};

const handleGenerate = async () => {
  if (!props.scheme) {
    ElMessage.error('没有可生成报告的方案数据');
    return;
  }

  try {
    await formRef.value?.validate();
    generateLoading.value = true;

    // 生成报告
    const result = await generateCloseValveReport(props.scheme.id!, reportConfig);
    
    if (result.data?.success) {
      // 导出报告
      const exportResult = await exportCloseValveReport(props.scheme.id!, reportConfig.format);
      
      // 下载文件
      const url = window.URL.createObjectURL(exportResult.data);
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', `${reportConfig.reportName}.${reportConfig.format.toLowerCase()}`);
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
      
      ElMessage.success('报告生成成功');
      handleClose();
    } else {
      ElMessage.error('报告生成失败');
    }
  } catch (error) {
    console.error('生成报告失败:', error);
    ElMessage.error('生成报告失败');
  } finally {
    generateLoading.value = false;
  }
};

// 暴露方法
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.report-generator {
  .dialog-footer {
    text-align: right;
  }
  
  .report-preview {
    max-height: 600px;
    overflow-y: auto;
    
    .preview-content {
      .report-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e4e7ed;
        
        h1 {
          margin: 0 0 15px 0;
          color: #303133;
          font-size: 24px;
        }
        
        .report-meta {
          color: #606266;
          font-size: 14px;
          
          p {
            margin: 5px 0;
          }
        }
      }
      
      .report-section {
        margin-bottom: 30px;
        
        h2 {
          margin: 0 0 15px 0;
          color: #303133;
          font-size: 18px;
          border-left: 4px solid #409eff;
          padding-left: 10px;
        }
        
        h3 {
          margin: 15px 0 10px 0;
          color: #606266;
          font-size: 16px;
        }
      }
      
      .map-images {
        display: flex;
        gap: 20px;
        
        .map-image-item {
          flex: 1;
          
          .image-placeholder {
            height: 200px;
            background: #f5f7fa;
            border: 2px dashed #dcdfe6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #909399;
            font-size: 14px;
          }
        }
      }
      
      .statistics-charts {
        .chart-placeholder {
          height: 300px;
          background: #f5f7fa;
          border: 2px dashed #dcdfe6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
