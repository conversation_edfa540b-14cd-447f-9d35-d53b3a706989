import request from '@/plugins/axios';

// ==================== 关阀分析相关接口 ====================

/**
 * 关阀分析方案数据结构
 */
export interface CloseValveScheme {
  id?: string;
  schemeName: string;
  schemeCode?: string;
  burstLocation: {
    x: number;
    y: number;
    address?: string;
  };
  analysisTime: string;
  analysisResults: {
    initialValves: string[];
    initialPipes: string[];
    secondaryValves: string[];
    secondaryPipes: string[];
    affectedUsers?: number;
    affectedPipeLength?: number;
  };
  summary: {
    totalValves: number;
    totalPipes: number;
    totalAffectedUsers: number;
    totalPipeLength: number;
  };
  creator?: string;
  createTime?: string;
  remark?: string;
}

/**
 * 关阀分析查询参数
 */
export interface CloseValveAnalysisQuery {
  page?: number;
  size?: number;
  schemeName?: string;
  schemeCode?: string;
  creator?: string;
  startTime?: string;
  endTime?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

/**
 * 执行关阀分析
 * @param params 分析参数
 */
export function executeCloseValveAnalysis(params: {
  burstLocation: {
    x: number;
    y: number;
  };
  layerName?: string;
  analysisType?: 'initial' | 'extended';
  extendedValves?: string[];
}) {
  return request({
    url: '/waterModels/valve/analysis/execute',
    method: 'post',
    data: params,
  });
  // 返回数据示例
  // {
  //   "success": true,
  //   "data": {
  //     "初始关闭阀门": ["V001", "V002"],
  //     "初始受影响管线": ["P001", "P002"],
  //     "二次关闭阀门": ["V003", "V004"],
  //     "二次关阀受影响管线": ["P003", "P004"],
  //     "影响用户数量": 1200,
  //     "停水管道长度": 2500.5
  //   }
  // }
}

/**
 * 保存关阀分析方案
 * @param scheme 方案数据
 */
export function saveCloseValveScheme(scheme: CloseValveScheme) {
  return request({
    url: '/waterModels/valve/scheme/save',
    method: 'post',
    data: scheme,
  });
}

/**
 * 更新关阀分析方案
 * @param scheme 方案数据
 */
export function updateCloseValveScheme(scheme: CloseValveScheme) {
  return request({
    url: `/waterModels/valve/scheme/update/${scheme.id}`,
    method: 'put',
    data: scheme,
  });
}

/**
 * 删除关阀分析方案
 * @param id 方案ID
 */
export function deleteCloseValveScheme(id: string) {
  return request({
    url: `/waterModels/valve/scheme/delete/${id}`,
    method: 'delete',
  });
}

/**
 * 批量删除关阀分析方案
 * @param ids 方案ID数组
 */
export function batchDeleteCloseValveSchemes(ids: string[]) {
  return request({
    url: '/waterModels/valve/scheme/batch-delete',
    method: 'delete',
    data: { ids },
  });
}

/**
 * 获取关阀分析方案列表
 * @param params 查询参数
 */
export function getCloseValveSchemeList(params: CloseValveAnalysisQuery) {
  return request({
    url: '/waterModels/valve/scheme/list',
    method: 'get',
    params,
  });
}

/**
 * 根据ID获取关阀分析方案详情
 * @param id 方案ID
 */
export function getCloseValveSchemeById(id: string) {
  return request({
    url: `/waterModels/valve/scheme/detail/${id}`,
    method: 'get',
  });
}

/**
 * 获取关阀分析历史记录
 * @param params 查询参数
 */
export function getCloseValveAnalysisHistory(params: CloseValveAnalysisQuery) {
  return request({
    url: '/waterModels/valve/analysis/history',
    method: 'get',
    params,
  });
}

/**
 * 生成关阀分析报告
 * @param schemeId 方案ID
 * @param reportConfig 报告配置
 */
export function generateCloseValveReport(
  schemeId: string,
  reportConfig: {
    format: 'PDF' | 'EXCEL' | 'WORD';
    includeMap?: boolean;
    includeStatistics?: boolean;
    includeDetails?: boolean;
    templateId?: string;
  }
) {
  return request({
    url: `/waterModels/valve/report/generate/${schemeId}`,
    method: 'post',
    data: reportConfig,
  });
}

/**
 * 导出关阀分析报告
 * @param schemeId 方案ID
 * @param format 导出格式
 */
export function exportCloseValveReport(schemeId: string, format: 'PDF' | 'EXCEL' | 'WORD' = 'PDF') {
  return request({
    url: `/waterModels/valve/report/export/${schemeId}`,
    method: 'get',
    params: { format },
    responseType: 'blob',
  });
}

/**
 * 获取关阀分析统计数据
 * @param params 统计参数
 */
export function getCloseValveAnalysisStatistics(params?: {
  startTime?: string;
  endTime?: string;
  groupBy?: 'day' | 'week' | 'month';
}) {
  return request({
    url: '/waterModels/valve/analysis/statistics',
    method: 'get',
    params,
  });
}

/**
 * 获取关阀影响范围分析
 * @param schemeId 方案ID
 */
export function getCloseValveImpactAnalysis(schemeId: string) {
  return request({
    url: `/waterModels/valve/impact/analysis/${schemeId}`,
    method: 'get',
  });
}

/**
 * 执行关阀扩散结果分析
 * @param params 分析参数
 */
export function executeCloseValveSpreadAnalysis(params: {
  schemeId: string;
  spreadRadius?: number;
  analysisDepth?: number;
  includeSecondaryImpact?: boolean;
}) {
  return request({
    url: '/waterModels/valve/spread/analysis',
    method: 'post',
    data: params,
  });
}

/**
 * 获取关阀方案模板列表
 */
export function getCloseValveSchemeTemplates() {
  return request({
    url: '/waterModels/valve/scheme/templates',
    method: 'get',
  });
}

/**
 * 基于模板创建关阀方案
 * @param templateId 模板ID
 * @param params 方案参数
 */
export function createSchemeFromTemplate(
  templateId: string,
  params: {
    schemeName: string;
    burstLocation: { x: number; y: number };
    customParams?: any;
  }
) {
  return request({
    url: `/waterModels/valve/scheme/create-from-template/${templateId}`,
    method: 'post',
    data: params,
  });
}

/**
 * 验证关阀方案有效性
 * @param schemeId 方案ID
 */
export function validateCloseValveScheme(schemeId: string) {
  return request({
    url: `/waterModels/valve/scheme/validate/${schemeId}`,
    method: 'post',
  });
}

/**
 * 获取关阀分析配置
 */
export function getCloseValveAnalysisConfig() {
  return request({
    url: '/waterModels/valve/analysis/config',
    method: 'get',
  });
}

/**
 * 更新关阀分析配置
 * @param config 配置数据
 */
export function updateCloseValveAnalysisConfig(config: {
  defaultAnalysisRadius?: number;
  maxAnalysisDepth?: number;
  enableSecondaryAnalysis?: boolean;
  reportTemplates?: any[];
}) {
  return request({
    url: '/waterModels/valve/analysis/config',
    method: 'put',
    data: config,
  });
}
