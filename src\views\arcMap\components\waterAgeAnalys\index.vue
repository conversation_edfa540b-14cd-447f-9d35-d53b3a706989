<!-- 水龄台账 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" :config="TableConfig" class="card-table"></CardTable>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
import { Download, Refresh, Search as SearchIcon } from '@element-plus/icons-vue';
import { accountColumns, moreFormDataFields } from './data';
import {
  queryWaterAgeHistory,
  exportWaterAgeReport,
  getAllNodes,
  getAllPipes,
} from '@/api/waterModels/waterAge';
import { SLMessage } from '@/utils/Message';

const refTable = ref<ICardTableIns>();
const refSearch = ref<ISearchIns>();

// 动态更新搜索字段选项
const updateSearchFieldOptions = async () => {
  try {
    // 获取节点选项
    const nodesResult = await getAllNodes();
    if (nodesResult.data?.success) {
      const nodeField = moreFormDataFields.find((field: any) => field.field === 'nodeIds');
      if (nodeField) {
        nodeField.options = (nodesResult.data.data || []).map((node: any) => ({
          label: `${node.name} (ID: ${node.id})`,
          value: node.id,
        }));
      }
    }

    // 获取管道选项
    const pipesResult = await getAllPipes();
    if (pipesResult.data?.success) {
      const pipeField = moreFormDataFields.find((field: any) => field.field === 'pipeIds');
      if (pipeField) {
        pipeField.options = (pipesResult.data.data || []).map((pipe: any) => ({
          label: `${pipe.name} (ID: ${pipe.id})`,
          value: pipe.id,
        }));
      }
    }
  } catch (error) {
    console.error('获取选项数据失败:', error);
  }
};
// 搜索配置
const SearchConfig = reactive<ISearch>({
  filters: [...moreFormDataFields],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData(),
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
          },
        },

        {
          perm: true,
          text: '导出',
          type: 'danger',
          svgIcon: shallowRef(Download),
          click: () => exportTable(),
        },
      ],
    },
  ],
});
// 列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: accountColumns,
  // operations: [
  //   {
  //     perm: true,
  //     text: '修改',
  //     svgIcon: shallowRef(Edit),
  //     click: row => {
  //       FormConfig.title = '修改';
  //       handleEdit(row);
  //     },
  //   },
  //   {
  //     perm: true,
  //     text: '删除',
  //     type: 'danger',
  //     svgIcon: shallowRef(Delete),
  //     click: row => handleDelete(row),
  //   },
  // ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    },
  },
});

// 导出列表
const exportTable = async () => {
  try {
    const query = refSearch.value?.queryParams || {};
    const [startTime, endTime] = query.timeRange || [];

    const params = {
      analysisTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      gradeConfigs: [
        {
          gradeName: '良好',
          minAge: 0,
          maxAge: 24,
          color: '#90EE90',
          description: '水质良好，正常范围',
          abnormal: false,
        },
        {
          gradeName: '一般',
          minAge: 24,
          maxAge: 48,
          color: '#FFD700',
          description: '水质一般',
          abnormal: false,
        },
        {
          gradeName: '异常',
          minAge: 48,
          maxAge: 999,
          color: '#FF6347',
          description: '水质异常，需要关注',
          abnormal: true,
        },
      ],
      abnormalThreshold: query.ageThreshold || 48,
      includeHeatmap: false,
      includeStatistics: true,
      markAbnormalNodes: true,
      regionGroupField: 'region',
      generateReport: true,
      exportFormat: 'PDF',
      startTime: startTime ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') : null,
      endTime: endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : null,
      nodeIds: query.nodeIds || [],
      pipeIds: query.pipeIds || [],
    };

    const res = await exportWaterAgeReport(params);
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', `水龄分析报告_${dayjs().format('YYYY-MM-DD')}.pdf`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    SLMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    SLMessage.error('导出失败，请重试');
  }
};

// 刷新数据
const refreshData = async () => {
  try {
    const query = refSearch.value?.queryParams || {};
    const [startTime, endTime] = query.timeRange || [];

    const params = {
      startTime: startTime ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') : undefined,
      endTime: endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : undefined,
      nodeIds: query.nodeIds || [],
      pipeIds: query.pipeIds || [],
      ageThreshold: query.ageThreshold || undefined,
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
    };

    const result = await queryWaterAgeHistory(params);

    if (result.data?.success) {
      TableConfig.pagination.total = result.data.data?.total || 0;
      TableConfig.dataList = result.data.data?.list || [];
    } else {
      SLMessage.error(result.data?.message || '查询失败');
      TableConfig.dataList = [];
      TableConfig.pagination.total = 0;
    }
  } catch (error) {
    console.error('查询数据失败:', error);
    SLMessage.error('查询数据失败，请重试');
    TableConfig.dataList = [];
    TableConfig.pagination.total = 0;
  }
};

onMounted(async () => {
  await updateSearchFieldOptions();
  await refreshData();
});
</script>
<style lang="scss" scoped>
.buttons {
  display: flex;
  align-items: flex-start;

  button {
    margin-right: 10px;
  }
}

.btns {
  display: flex;

  .el-button {
    margin-right: 20px;
  }
}

.form-table {
  height: 40vh;
}
</style>
