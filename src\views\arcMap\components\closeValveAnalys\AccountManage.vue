<!-- 关阀分析台账管理 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" :config="TableConfig" class="card-table"></CardTable>

    <!-- 新增/编辑弹框 -->
    <DialogForm ref="refForm" :config="FormConfig"></DialogForm>

    <!-- 详情查看弹框 -->
    <DialogForm ref="refDetailDialog" :config="detailConfig">
      <div class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="台账编码">{{
            state.currentDetail?.accountCode
          }}</el-descriptions-item>
          <el-descriptions-item label="台账名称">{{
            state.currentDetail?.accountName
          }}</el-descriptions-item>
          <el-descriptions-item label="分析日期">{{
            formatDate(state.currentDetail?.analysisDate)
          }}</el-descriptions-item>
          <el-descriptions-item label="分析类型">{{
            getAnalysisTypeLabel(state.currentDetail?.analysisType)
          }}</el-descriptions-item>
          <el-descriptions-item label="优先级">{{
            getPriorityLabel(state.currentDetail?.priority)
          }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{
            getStatusLabel(state.currentDetail?.status)
          }}</el-descriptions-item>
          <el-descriptions-item label="爆管位置">{{
            state.currentDetail?.burstLocation?.address
          }}</el-descriptions-item>
          <el-descriptions-item label="影响阀门数"
            >{{ state.currentDetail?.valveCount }}个</el-descriptions-item
          >
          <el-descriptions-item label="影响管线长度"
            >{{ state.currentDetail?.pipeLength }}米</el-descriptions-item
          >
          <el-descriptions-item label="影响用户数"
            >{{ state.currentDetail?.affectedUsers }}户</el-descriptions-item
          >
          <el-descriptions-item label="预计停水时长"
            >{{ state.currentDetail?.estimatedDuration }}小时</el-descriptions-item
          >
          <el-descriptions-item label="实际停水时长"
            >{{ state.currentDetail?.actualDuration || '未完成' }}小时</el-descriptions-item
          >
          <el-descriptions-item label="操作员">{{
            state.currentDetail?.operator
          }}</el-descriptions-item>
          <el-descriptions-item label="审核员">{{
            state.currentDetail?.reviewer || '未审核'
          }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{
            formatDateTime(state.currentDetail?.createTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{
            formatDateTime(state.currentDetail?.completeTime) || '未完成'
          }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="state.currentDetail?.remark" class="remark-section">
          <h4>备注信息</h4>
          <p>{{ state.currentDetail.remark }}</p>
        </div>

        <div v-if="state.currentDetail?.attachments?.length" class="attachment-section">
          <h4>相关附件</h4>
          <el-table :data="state.currentDetail.attachments" size="small">
            <el-table-column prop="fileName" label="文件名"></el-table-column>
            <el-table-column prop="fileType" label="文件类型"></el-table-column>
            <el-table-column
              prop="uploadTime"
              label="上传时间"
              :formatter="row => formatDateTime(row.uploadTime)"
            ></el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="downloadAttachment(row)"
                  >下载</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </DialogForm>

    <!-- 导入弹框 -->
    <DialogForm ref="refUploadDialog" :config="uploadConfig" class="upload-dialog">
      <el-form ref="formRef">
        <el-form-item>
          <div class="buttons">
            <el-button type="primary" :icon="Download" :plain="true" @click="downloadTemplate">
              下载模板
            </el-button>
            <el-upload
              ref="upload"
              action="action"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              :show-file-list="true"
              :on-remove="handleRemove"
              :limit="1"
              :auto-upload="false"
              :on-change="clickUpload"
              class="upload-demo"
            >
              <el-button type="primary" :icon="Upload"> 添加文件 </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能导入xlsx文件, 请确保导入的文件单元格格式为文本!
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <FormTable :config="uploadTableConfig"></FormTable>
    </DialogForm>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import {
  Delete,
  Download,
  Edit,
  Plus,
  Refresh,
  Search as SearchIcon,
  Upload,
  View,
  DocumentCopy,
  FolderOpened,
} from '@element-plus/icons-vue';
import { shallowRef } from 'vue';
import { ElMessage } from 'element-plus';
import { SLConfirm } from '@/utils/Message.js';
import { downloadFile } from '@/utils/fileHelper';
import {
  getCloseValveAccountList,
  getCloseValveAccountById,
  addCloseValveAccount,
  updateCloseValveAccount,
  deleteCloseValveAccount,
  batchDeleteCloseValveAccounts,
  exportCloseValveAccounts,
  getCloseValveAccountTemplate,
  importCloseValveAccounts,
  copyCloseValveAccount,
  archiveCloseValveAccount,
  type CloseValveAccount,
  type CloseValveAccountQuery,
} from '@/api/waterModels/closeValveAccount';
import { useUserStore } from '@/store';
import useGlobal from '@/hooks/global/useGlobal';

const { $messageSuccess, $messageError } = useGlobal();

const state = reactive<{
  dataList: CloseValveAccount[];
  currentDetail?: CloseValveAccount;
  selectedIds: string[];
}>({
  dataList: [],
  currentDetail: undefined,
  selectedIds: [],
});

const refForm = ref<IDialogFormIns>();
const refTable = ref<ICardTableIns>();
const refDetailDialog = ref<IDialogFormIns>();
const refUploadDialog = ref<IDialogFormIns>();
const refSearch = ref<ISearchIns>();

// 分析类型选项
const analysisTypeOptions = [
  { label: '应急分析', value: 'emergency' },
  { label: '计划分析', value: 'planned' },
  { label: '维护分析', value: 'maintenance' },
];

// 优先级选项
const priorityOptions = [
  { label: '高', value: 'high' },
  { label: '中', value: 'medium' },
  { label: '低', value: 'low' },
];

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '分析中', value: 'analyzing' },
  { label: '已完成', value: 'completed' },
  { label: '已归档', value: 'archived' },
];

// 搜索配置
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      label: '台账编码',
      field: 'accountCode',
      placeholder: '请输入台账编码',
    },
    {
      type: 'input',
      label: '台账名称',
      field: 'accountName',
      placeholder: '请输入台账名称',
    },
    {
      type: 'select',
      label: '分析类型',
      field: 'analysisType',
      placeholder: '请选择分析类型',
      options: analysisTypeOptions,
    },
    {
      type: 'select',
      label: '优先级',
      field: 'priority',
      placeholder: '请选择优先级',
      options: priorityOptions,
    },
    {
      type: 'select',
      label: '状态',
      field: 'status',
      placeholder: '请选择状态',
      options: statusOptions,
    },
    {
      type: 'input',
      label: '操作员',
      field: 'operator',
      placeholder: '请输入操作员',
    },
    {
      type: 'daterange',
      label: '分析日期',
      field: 'dateRange',
      format: 'YYYY-MM-DD',
    },
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData(),
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
          },
        },
        {
          perm: true,
          text: '新增',
          type: 'success',
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.title = '新增关阀分析台账';
            handleEdit();
          },
        },
        {
          perm: true,
          text: '导入',
          type: 'warning',
          svgIcon: shallowRef(Upload),
          click: () => {
            handleRemove();
            refUploadDialog.value?.openDialog();
          },
        },
        {
          perm: true,
          text: '导出',
          type: 'danger',
          svgIcon: shallowRef(Download),
          click: () => exportTable(),
        },
        {
          perm: true,
          text: '批量删除',
          type: 'danger',
          disabled: () => state.selectedIds.length === 0,
          click: () => handleBatchDelete(),
        },
        {
          perm: true,
          text: '批量归档',
          type: 'info',
          disabled: () => state.selectedIds.length === 0,
          click: () => handleBatchArchive(),
        },
      ],
    },
  ],
});

// 工具函数
const formatDate = (date: string) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '';
};

const formatDateTime = (date: string) => {
  return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '';
};

const getAnalysisTypeLabel = (type: string) => {
  return analysisTypeOptions.find(item => item.value === type)?.label || type;
};

const getPriorityLabel = (priority: string) => {
  return priorityOptions.find(item => item.value === priority)?.label || priority;
};

const getStatusLabel = (status: string) => {
  return statusOptions.find(item => item.value === status)?.label || status;
};

const downloadAttachment = (attachment: any) => {
  downloadFile(attachment.fileUrl, attachment.fileName);
};

// 刷新数据
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {};
  const [startDate, endDate] = query.dateRange || [];
  const params: CloseValveAccountQuery = {
    ...query,
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD') : undefined,
    endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : undefined,
  };

  try {
    const result = await getCloseValveAccountList(params);
    TableConfig.pagination.total = result.data?.total || 0;
    TableConfig.dataList = result.data?.data || [];
    state.dataList = result.data?.data || [];
  } catch (error) {
    console.error('获取关阀分析台账列表失败:', error);
    $messageError('获取数据失败');
  }
};

// 列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  selectable: true,
  columns: [
    { label: '台账编码', prop: 'accountCode', minWidth: 120 },
    { label: '台账名称', prop: 'accountName', minWidth: 150 },
    {
      label: '分析日期',
      prop: 'analysisDate',
      minWidth: 120,
      formatter: (row: any, value: any) => formatDate(value),
    },
    {
      label: '分析类型',
      prop: 'analysisType',
      minWidth: 100,
      formatter: (row: any, value: any) => getAnalysisTypeLabel(value),
    },
    {
      label: '优先级',
      prop: 'priority',
      minWidth: 80,
      formatter: (row: any, value: any) => getPriorityLabel(value),
    },
    {
      label: '状态',
      prop: 'status',
      minWidth: 80,
      formatter: (row: any, value: any) => getStatusLabel(value),
    },
    { label: '爆管位置', prop: 'burstLocation.address', minWidth: 200 },
    {
      label: '影响阀门数',
      prop: 'valveCount',
      minWidth: 100,
      formatter: (row: any, value: any) => `${value}个`,
    },
    {
      label: '影响用户数',
      prop: 'affectedUsers',
      minWidth: 100,
      formatter: (row: any, value: any) => `${value}户`,
    },
    { label: '操作员', prop: 'operator', minWidth: 100 },
    {
      label: '创建时间',
      prop: 'createTime',
      minWidth: 150,
      formatter: (row: any, value: any) => formatDateTime(value),
    },
  ],
  operations: [
    {
      perm: true,
      text: '查看',
      svgIcon: shallowRef(View),
      click: row => handleViewDetail(row),
    },
    {
      perm: true,
      text: '编辑',
      svgIcon: shallowRef(Edit),
      disabled: (row: any) => row.status === 'archived',
      click: row => {
        FormConfig.title = '编辑关阀分析台账';
        handleEdit(row);
      },
    },
    {
      perm: true,
      text: '复制',
      svgIcon: shallowRef(DocumentCopy),
      click: row => handleCopy(row),
    },
    {
      perm: true,
      text: '归档',
      type: 'warning',
      disabled: (row: any) => row.status === 'archived',
      click: row => handleArchive(row),
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: row => handleDelete(row),
    },
  ],
  dataList: [],
  handleSelectChange: (rows: any[]) => {
    state.selectedIds = rows.map(row => row.id);
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    },
  },
});

// 表单配置
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 800,
  title: '新增关阀分析台账',
  labelWidth: 120,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '台账编码',
          field: 'accountCode',
          placeholder: '请输入台账编码',
          rules: [{ required: true, message: '请输入台账编码', trigger: 'blur' }],
        },
        {
          type: 'input',
          label: '台账名称',
          field: 'accountName',
          placeholder: '请输入台账名称',
          rules: [{ required: true, message: '请输入台账名称', trigger: 'blur' }],
        },
        {
          type: 'date',
          label: '分析日期',
          field: 'analysisDate',
          rules: [{ required: true, message: '请选择分析日期', trigger: 'blur' }],
        },
        {
          type: 'select',
          label: '分析类型',
          field: 'analysisType',
          placeholder: '请选择分析类型',
          options: analysisTypeOptions,
          rules: [{ required: true, message: '请选择分析类型', trigger: 'blur' }],
        },
        {
          type: 'select',
          label: '优先级',
          field: 'priority',
          placeholder: '请选择优先级',
          options: priorityOptions,
          rules: [{ required: true, message: '请选择优先级', trigger: 'blur' }],
        },
        {
          type: 'select',
          label: '状态',
          field: 'status',
          placeholder: '请选择状态',
          options: statusOptions,
          rules: [{ required: true, message: '请选择状态', trigger: 'blur' }],
        },
        {
          type: 'input',
          label: '爆管地址',
          field: 'burstLocation.address',
          placeholder: '请输入爆管位置地址',
          rules: [{ required: true, message: '请输入爆管位置地址', trigger: 'blur' }],
        },
        {
          type: 'input-number',
          label: '影响阀门数',
          field: 'valveCount',
          placeholder: '请输入影响阀门数量',
          rules: [{ required: true, message: '请输入影响阀门数量', trigger: 'blur' }],
        },
        {
          type: 'input-number',
          label: '影响用户数',
          field: 'affectedUsers',
          placeholder: '请输入影响用户数量',
          rules: [{ required: true, message: '请输入影响用户数量', trigger: 'blur' }],
        },
        {
          type: 'input-number',
          label: '管线长度(米)',
          field: 'pipeLength',
          placeholder: '请输入影响管线长度',
          rules: [{ required: true, message: '请输入影响管线长度', trigger: 'blur' }],
        },
        {
          type: 'input-number',
          label: '预计停水时长(小时)',
          field: 'estimatedDuration',
          placeholder: '请输入预计停水时长',
          rules: [{ required: true, message: '请输入预计停水时长', trigger: 'blur' }],
        },
        {
          type: 'input',
          label: '操作员',
          field: 'operator',
          placeholder: '请输入操作员',
          rules: [{ required: true, message: '请输入操作员', trigger: 'blur' }],
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark',
          placeholder: '请输入备注信息',
        },
      ],
    },
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(() => {
        if (FormConfig.title.includes('新增')) {
          addCloseValveAccount(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('新增成功');
            refreshData();
          });
        } else {
          updateCloseValveAccount(params).then(() => {
            refForm.value?.refForm?.resetForm();
            refForm.value?.closeDialog();
            ElMessage.success('更新成功');
            refreshData();
          });
        }
      })
      .catch(() => {
        //
      });
  },
});

// 详情弹框配置
const detailConfig = reactive<IDialogFormConfig>({
  dialogWidth: 1000,
  title: '关阀分析台账详情',
  group: [],
  cancel: false,
  btns: [
    {
      perm: true,
      text: '关闭',
      click: () => {
        refDetailDialog.value?.closeDialog();
      },
    },
  ],
});

// 导入弹框配置
const uploadConfig = reactive<IDialogFormConfig>({
  title: '导入关阀分析台账',
  dialogWidth: 1200,
  group: [],
  cancel: true,
  btns: [
    {
      perm: true,
      text: '确定导入',
      click: () => {
        if (state.dataList.length > 0) {
          importCloseValveAccounts(state.dataList as any)
            .then(res => {
              if (res.data?.success) {
                handleRemove();
                refUploadDialog.value?.closeDialog();
                ElMessage.success('导入成功');
                refreshData();
              } else {
                ElMessage.error('导入失败');
              }
            })
            .catch(error => {
              console.log(error);
              ElMessage.error('导入失败');
            });
        } else {
          ElMessage.warning('请导入正确的xlsx文件！');
        }
      },
    },
  ],
});

const uploadTableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: [
    { label: '台账编码', prop: 'accountCode', minWidth: 120 },
    { label: '台账名称', prop: 'accountName', minWidth: 150 },
    { label: '分析类型', prop: 'analysisType', minWidth: 100 },
    { label: '优先级', prop: 'priority', minWidth: 80 },
    { label: '爆管位置', prop: 'burstLocation.address', minWidth: 200 },
    { label: '操作员', prop: 'operator', minWidth: 100 },
  ],
  dataList: [],
  pagination: {
    hide: true,
  },
});

// 处理函数
const handleViewDetail = async (row: CloseValveAccount) => {
  try {
    const result = await getCloseValveAccountById(row.id!);
    state.currentDetail = result.data;
    refDetailDialog.value?.openDialog();
  } catch (error) {
    console.error('获取台账详情失败:', error);
    $messageError('获取详情失败');
  }
};

const handleEdit = (row?: CloseValveAccount) => {
  FormConfig.defaultValue = row ? { ...row } : {};
  refForm.value?.openDialog();
};

const handleDelete = (row: CloseValveAccount) => {
  SLConfirm('确定删除该台账？', '删除确认')
    .then(() => {
      deleteCloseValveAccount(row.id!).then(() => {
        ElMessage.success('删除成功');
        refreshData();
      });
    })
    .catch(() => {
      //
    });
};

const handleBatchDelete = () => {
  SLConfirm(`确定删除选中的 ${state.selectedIds.length} 条台账？`, '批量删除确认')
    .then(() => {
      batchDeleteCloseValveAccounts(state.selectedIds).then(() => {
        ElMessage.success('批量删除成功');
        state.selectedIds = [];
        refreshData();
      });
    })
    .catch(() => {
      //
    });
};

const handleCopy = (row: CloseValveAccount) => {
  const newAccountData = {
    accountCode: `${row.accountCode}_copy_${Date.now()}`,
    accountName: `${row.accountName}_副本`,
    analysisDate: dayjs().format('YYYY-MM-DD'),
  };

  copyCloseValveAccount(row.id!, newAccountData)
    .then(() => {
      ElMessage.success('复制成功');
      refreshData();
    })
    .catch(() => {
      $messageError('复制失败');
    });
};

const handleArchive = (row: CloseValveAccount) => {
  SLConfirm('确定归档该台账？归档后将无法编辑。', '归档确认')
    .then(() => {
      archiveCloseValveAccount(row.id!).then(() => {
        ElMessage.success('归档成功');
        refreshData();
      });
    })
    .catch(() => {
      //
    });
};

const handleBatchArchive = () => {
  SLConfirm(`确定归档选中的 ${state.selectedIds.length} 条台账？`, '批量归档确认')
    .then(() => {
      // 这里需要实现批量归档API
      Promise.all(state.selectedIds.map(id => archiveCloseValveAccount(id)))
        .then(() => {
          ElMessage.success('批量归档成功');
          state.selectedIds = [];
          refreshData();
        })
        .catch(() => {
          $messageError('批量归档失败');
        });
    })
    .catch(() => {
      //
    });
};

const exportTable = async () => {
  const query = refSearch.value?.queryParams || {};
  const [startDate, endDate] = query.dateRange || [];
  const params = {
    query: {
      ...query,
      startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD') : undefined,
      endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : undefined,
    },
    format: 'excel' as const,
    includeDetails: true,
  };

  try {
    const res = await exportCloseValveAccounts(params);
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', `关阀分析台账列表.xlsx`);
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('导出失败:', error);
    $messageError('导出失败');
  }
};

const downloadTemplate = async () => {
  try {
    const res = await getCloseValveAccountTemplate();
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', `关阀分析台账模板.xlsx`);
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载模板失败:', error);
    $messageError('下载模板失败');
  }
};

const handleRemove = () => {
  state.dataList = [];
  uploadTableConfig.dataList = [];
};

const clickUpload = (file: any) => {
  // 这里应该解析Excel文件，暂时模拟数据
  state.dataList = [];
  // 实际实现中需要使用类似 XLSX 库来解析文件
  ElMessage.info('文件解析功能需要实现');
};

onMounted(async () => {
  await refreshData();
});
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
}

.detail-content {
  .remark-section,
  .attachment-section {
    margin-top: 20px;

    h4 {
      margin-bottom: 10px;
      color: #303133;
    }
  }
}

.buttons {
  display: flex;
  align-items: flex-start;

  button {
    margin-right: 10px;
  }
}

.upload-demo {
  margin-right: 10px;
}
</style>
