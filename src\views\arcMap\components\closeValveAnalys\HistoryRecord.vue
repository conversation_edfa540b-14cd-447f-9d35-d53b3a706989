<!-- 关阀方案历史记录 -->
<template>
  <div class="history-record">
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="方案名称">
          <el-input v-model="searchForm.schemeName" placeholder="请输入方案名称" clearable />
        </el-form-item>
        <el-form-item label="分析类型">
          <el-select v-model="searchForm.analysisType" placeholder="请选择分析类型" clearable>
            <el-option label="应急分析" value="emergency" />
            <el-option label="计划分析" value="planned" />
            <el-option label="维护分析" value="maintenance" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="searchForm.priority" placeholder="请选择优先级" clearable>
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="分析中" value="analyzing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-section">
      <div class="table-header">
        <h3>历史记录列表</h3>
        <div class="table-actions">
          <el-button type="danger" :disabled="selectedIds.length === 0" @click="handleBatchDelete">
            批量删除
          </el-button>
          <el-button type="info" :disabled="selectedIds.length === 0" @click="handleBatchArchive">
            批量归档
          </el-button>
          <el-button type="success" @click="handleExport">导出记录</el-button>
        </div>
      </div>

      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="schemeName" label="方案名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="analysisDate" label="分析日期" width="120" />
        <el-table-column prop="analysisType" label="分析类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getAnalysisTypeTagType(row.analysisType)">
              {{ getAnalysisTypeLabel(row.analysisType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="burstLocation.address" label="爆管位置" min-width="200" show-overflow-tooltip />
        <el-table-column prop="valveCount" label="影响阀门" width="100">
          <template #default="{ row }">{{ row.valveCount }}个</template>
        </el-table-column>
        <el-table-column prop="affectedUsers" label="影响用户" width="100">
          <template #default="{ row }">{{ row.affectedUsers }}户</template>
        </el-table-column>
        <el-table-column prop="operator" label="操作员" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="150">
          <template #default="{ row }">{{ formatDateTime(row.createTime) }}</template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleLoadToMap(row)">加载到地图</el-button>
            <el-button type="info" size="small" @click="handleGenerateReport(row)">生成报告</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情弹框 -->
    <el-dialog v-model="detailVisible" title="方案详情" width="80%" :close-on-click-modal="false">
      <div v-if="currentDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="方案名称">{{ currentDetail.schemeName }}</el-descriptions-item>
          <el-descriptions-item label="分析日期">{{ currentDetail.analysisDate }}</el-descriptions-item>
          <el-descriptions-item label="分析类型">{{ getAnalysisTypeLabel(currentDetail.analysisType) }}</el-descriptions-item>
          <el-descriptions-item label="优先级">{{ getPriorityLabel(currentDetail.priority) }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ getStatusLabel(currentDetail.status) }}</el-descriptions-item>
          <el-descriptions-item label="爆管位置">{{ currentDetail.burstLocation?.address }}</el-descriptions-item>
          <el-descriptions-item label="影响阀门数">{{ currentDetail.valveCount }}个</el-descriptions-item>
          <el-descriptions-item label="影响用户数">{{ currentDetail.affectedUsers }}户</el-descriptions-item>
          <el-descriptions-item label="管线长度">{{ currentDetail.pipeLength }}米</el-descriptions-item>
          <el-descriptions-item label="预计停水时长">{{ currentDetail.estimatedDuration }}小时</el-descriptions-item>
          <el-descriptions-item label="操作员">{{ currentDetail.operator }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentDetail.createTime) }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="currentDetail.remark" class="remark-section">
          <h4>备注信息</h4>
          <p>{{ currentDetail.remark }}</p>
        </div>

        <div v-if="currentDetail.analysisResults" class="analysis-results-section">
          <h4>分析结果</h4>
          <el-tabs>
            <el-tab-pane label="初始关闭阀门" :name="'initialValves'">
              <el-table :data="currentDetail.analysisResults.initialValves" size="small" max-height="300">
                <el-table-column prop="id" label="阀门ID" />
                <el-table-column prop="name" label="阀门名称" />
                <el-table-column prop="type" label="阀门类型" />
                <el-table-column prop="status" label="状态" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="初始受影响管线" :name="'initialPipes'">
              <el-table :data="currentDetail.analysisResults.initialPipes" size="small" max-height="300">
                <el-table-column prop="id" label="管线ID" />
                <el-table-column prop="name" label="管线名称" />
                <el-table-column prop="length" label="长度(米)" />
                <el-table-column prop="material" label="材质" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="二次关闭阀门" :name="'secondaryValves'">
              <el-table :data="currentDetail.analysisResults.secondaryValves" size="small" max-height="300">
                <el-table-column prop="id" label="阀门ID" />
                <el-table-column prop="name" label="阀门名称" />
                <el-table-column prop="type" label="阀门类型" />
                <el-table-column prop="status" label="状态" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="二次受影响管线" :name="'secondaryPipes'">
              <el-table :data="currentDetail.analysisResults.secondaryPipes" size="small" max-height="300">
                <el-table-column prop="id" label="管线ID" />
                <el-table-column prop="name" label="管线名称" />
                <el-table-column prop="length" label="长度(米)" />
                <el-table-column prop="material" label="材质" />
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleLoadToMap(currentDetail)">加载到地图</el-button>
        <el-button type="success" @click="handleGenerateReport(currentDetail)">生成报告</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import {
  getCloseValveSchemeList,
  getCloseValveSchemeById,
  deleteCloseValveScheme,
  batchDeleteCloseValveSchemes,
  archiveCloseValveScheme,
  batchArchiveCloseValveSchemes,
  exportCloseValveSchemes,
  generateCloseValveReport,
  exportCloseValveReport,
  type CloseValveScheme,
  type CloseValveSchemeQuery,
} from '@/api/waterModels/closeValveAnalysis';

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const tableData = ref<CloseValveScheme[]>([]);
const currentDetail = ref<CloseValveScheme | null>(null);
const selectedIds = ref<string[]>([]);

const searchForm = reactive<CloseValveSchemeQuery>({
  schemeName: '',
  analysisType: '',
  priority: '',
  status: '',
  dateRange: [],
});

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
});

// 定义事件
const emit = defineEmits<{
  loadToMap: [scheme: CloseValveScheme];
}>();

// 工具函数
const formatDateTime = (date: string) => {
  return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '';
};

const getAnalysisTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    emergency: '应急分析',
    planned: '计划分析',
    maintenance: '维护分析',
  };
  return map[type] || type;
};

const getAnalysisTypeTagType = (type: string) => {
  const map: Record<string, string> = {
    emergency: 'danger',
    planned: 'warning',
    maintenance: 'info',
  };
  return map[type] || '';
};

const getPriorityLabel = (priority: string) => {
  const map: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低',
  };
  return map[priority] || priority;
};

const getPriorityTagType = (priority: string) => {
  const map: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info',
  };
  return map[priority] || '';
};

const getStatusLabel = (status: string) => {
  const map: Record<string, string> = {
    draft: '草稿',
    analyzing: '分析中',
    completed: '已完成',
    archived: '已归档',
  };
  return map[status] || status;
};

const getStatusTagType = (status: string) => {
  const map: Record<string, string> = {
    draft: 'info',
    analyzing: 'warning',
    completed: 'success',
    archived: 'info',
  };
  return map[status] || '';
};

// 数据加载
const loadData = async () => {
  loading.value = true;
  try {
    const [startDate, endDate] = searchForm.dateRange || [];
    const params: CloseValveSchemeQuery = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size,
      startDate,
      endDate,
    };
    
    const result = await getCloseValveSchemeList(params);
    tableData.value = result.data?.data || [];
    pagination.total = result.data?.total || 0;
  } catch (error) {
    console.error('加载历史记录失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 事件处理
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    schemeName: '',
    analysisType: '',
    priority: '',
    status: '',
    dateRange: [],
  });
  handleSearch();
};

const handleSelectionChange = (selection: CloseValveScheme[]) => {
  selectedIds.value = selection.map(item => item.id!);
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadData();
};

const handleView = async (row: CloseValveScheme) => {
  try {
    const result = await getCloseValveSchemeById(row.id!);
    currentDetail.value = result.data;
    detailVisible.value = true;
  } catch (error) {
    console.error('获取方案详情失败:', error);
    ElMessage.error('获取详情失败');
  }
};

const handleLoadToMap = (row: CloseValveScheme) => {
  emit('loadToMap', row);
  ElMessage.success('方案已加载到地图');
};

const handleGenerateReport = async (row: CloseValveScheme) => {
  try {
    const reportConfig = {
      includeMap: true,
      includeStatistics: true,
      includeDetails: true,
      format: 'PDF',
    };
    
    // 模拟报告生成
    ElMessage.success('报告生成功能开发中，请稍后');
  } catch (error) {
    console.error('生成报告失败:', error);
    ElMessage.error('生成报告失败');
  }
};

const handleDelete = async (row: CloseValveScheme) => {
  try {
    await ElMessageBox.confirm('确定删除该方案？', '删除确认', {
      type: 'warning',
    });
    
    await deleteCloseValveScheme(row.id!);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定删除选中的 ${selectedIds.value.length} 条记录？`, '批量删除确认', {
      type: 'warning',
    });
    
    await batchDeleteCloseValveSchemes(selectedIds.value);
    ElMessage.success('批量删除成功');
    selectedIds.value = [];
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }
};

const handleBatchArchive = async () => {
  try {
    await ElMessageBox.confirm(`确定归档选中的 ${selectedIds.value.length} 条记录？`, '批量归档确认', {
      type: 'warning',
    });
    
    await batchArchiveCloseValveSchemes(selectedIds.value);
    ElMessage.success('批量归档成功');
    selectedIds.value = [];
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量归档失败:', error);
      ElMessage.error('批量归档失败');
    }
  }
};

const handleExport = async () => {
  try {
    const [startDate, endDate] = searchForm.dateRange || [];
    const params = {
      query: {
        ...searchForm,
        startDate,
        endDate,
      },
      format: 'excel' as const,
      includeDetails: true,
    };
    
    const res = await exportCloseValveSchemes(params);
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', `关阀方案历史记录.xlsx`);
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};

// 初始化
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.history-record {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .search-section {
    padding: 16px;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .table-section {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        color: #303133;
      }
      
      .table-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .el-table {
      flex: 1;
    }
    
    .el-pagination {
      margin-top: 16px;
      justify-content: center;
    }
  }
  
  .detail-content {
    .remark-section,
    .analysis-results-section {
      margin-top: 20px;
      
      h4 {
        margin-bottom: 10px;
        color: #303133;
      }
    }
  }
}
</style>
