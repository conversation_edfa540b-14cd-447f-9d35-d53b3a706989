<template>
  <RightDrawerMap ref="refMap" :title="'关阀分析'" @map-loaded="onMapLoaded">
    <Form ref="refForm" :config="FormConfig"></Form>

    <!-- 关阀分析结果汇总 -->
    <div v-if="state.analysisResults.length > 0" class="burst-stats">
      <div class="stats-title">影响范围统计</div>
      <div class="stats-content">
        <div v-for="layerData in state.analysisResults" :key="layerData.layerName" class="stats-item">
          <div class="color-box" :style="{ backgroundColor: getLayerColor(layerData.layerName) }"></div>
          <span class="layer-label">{{ layerData.layerName }}:</span>
          <span class="layer-count">{{ layerData.count
          }}{{ layerData.geometryType === 'esriGeometryPoint' ? '个' : '米' }}</span>
        </div>
      </div>
      <div class="stats-summary">
        总计:
        {{state.analysisResults.reduce((sum, layer) => sum + layer.count, 0)}}个要素
      </div>
    </div>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Extent from '@arcgis/core/geometry/Extent.js';
import { getGraphicLayer, setMapCursor, setSymbol } from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { excuteIdentifyByGeoserver } from '@/utils/geoserver/geoserverUtils';
import { burstpipeAnalysis } from '@/utils/geoserver/gisAnalyse';
import { QueryByPolygon } from '@/utils/geoserver/wfsUtils';
import Point from '@arcgis/core/geometry/Point';
import Polyline from '@arcgis/core/geometry/Polyline';
import Graphic from '@arcgis/core/Graphic';
import { nextTick } from 'vue';

// @ts-ignore
import RightDrawerMap from '../common/RightDrawerMap.vue';

const refForm = ref<IFormIns>();

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();
const state = reactive<{
  curOperate: 'picking' | 'picked' | 'analysing' | 'analysed' | '';
  selectedPipe?: {
    layerId: string;
    layerName: string;
    attributes: any;
    geometry: __esri.Geometry;
  };
  analysisResults: any[];
  valveAnalysisResults?: {
    initialValves?: string[];
    initialPipes?: string[];
    secondaryValves?: string[];
    secondaryPipes?: string[];
  };
}>({
  curOperate: '',
  selectedPipe: undefined,
  analysisResults: [],
  valveAnalysisResults: undefined,
});
const staticState: {
  view?: __esri.MapView;
  identifyResult?: any;
  markLayer?: __esri.GraphicsLayer;
  mapClick: any;
  queryParams: any;
  burstPoint?: any;
  jobid?: string;
  resultLayer?: __esri.GraphicsLayer | __esri.MapImageLayer;
  valveFlag: boolean;
  valveCode?: any;
  resultSummary?:
  | {
    totalValves?: number;
    totalPipes?: number;
    layersummary?: Array<{
      layername: string;
      count: number;
      geometrytype: string;
    }>;
  }
  | any;

  // 存储实际查询到的要素数据
  queriedFeatures: {
    valves: any[];
    pipes: any[];
  };
} = {
  view: undefined,
  identifyResult: undefined,
  markLayer: undefined,
  mapClick: undefined,
  queryParams: {},
  jobid: '',
  valveFlag: false,
  queriedFeatures: {
    valves: [],
    pipes: [],
  },
};

const refMap = ref<InstanceType<typeof RightDrawerMap>>();

const onMapLoaded = (view: __esri.MapView) => {
  staticState.view = view;
};

const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '选取管线',
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%',
              },
              type: 'warning',
              text: () => (state.curOperate === 'picking' ? '正在选取管线' : '点击选取管线'),
              loading: () => state.curOperate === 'picking',
              disabled: () => ['picking', 'analysing'].indexOf(state.curOperate) !== -1,
              click: () => pickPipe(),
            },
          ],
        },
      ],
    },
    {
      fieldset: {
        desc: '操作',
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%',
              },
              loading: () => state.curOperate === 'analysing',
              text: () => (state.curOperate === 'analysing' ? '正在分析' : '开始分析'),
              disabled: () =>
                ['picking', 'analysing'].indexOf(state.curOperate) !== -1 || !state.selectedPipe,
              click: () => startAnalys(),
            },
            {
              perm: true,
              styles: {
                width: '100%',
              },
              type: 'danger',
              disabled: () => ['picking', 'analysing'].indexOf(state.curOperate) !== -1,
              text: '清除所有',
              click: () => clear(),
            },
          ],
        },
      ],
    },
    {
      fieldset: {
        desc: '分析结果',
      },
      fields: [
        {
          label: '地图显示',
          type: 'checkbox',
          field: 'showInMap',
          options: [{ label: '显示分析结果', value: 'show' }],
          onChange: val => {
            staticState.resultLayer && (staticState.resultLayer.visible = !!val.length);
          },
        },
      ],
    },
  ],
  defaultValue: {
    showInMap: ['show'],
  },
});

const pickPipe = () => {
  console.log('pickPipe 函数被调用');
  initDraw();
};

const initDraw = () => {
  const view = staticState.view;
  if (!view) {
    console.error('地图对象未初始化');
    SLMessage.error('地图未加载完成，请稍后重试');
    return;
  }

  console.log('initDraw 被调用，view对象:', view);
  setMapCursor('crosshair');
  state.curOperate = 'picking';

  staticState.markLayer = getGraphicLayer(view, {
    id: 'burst-mark',
    title: '关阀标注',
  });

  staticState.mapClick = view.on('click', async e => {
    console.log('地图点击事件触发');
    staticState.markLayer?.removeAll();
    await doIdentifyBurstPoint(e);
  });

  console.log('地图点击事件监听器已设置');
};

const doIdentifyBurstPoint = async (e: any) => {
  const view = staticState.view;
  if (!view) {
    console.error('地图对象未初始化');
    return;
  }

  try {
    console.log('开始关阀点识别，点击事件:', e);
    console.log('地图点击位置:', e.mapPoint);

    // 使用GeoServer进行点击查询
    const res = await excuteIdentifyByGeoserver(
      view,
      '/geoserver/guazhou/wms',
      'guazhou:管线', // 管线图层名称
      e,
    );

    console.log('GeoServer查询结果:', res);

    if (!res || !res.data || !res.data.features || res.data.features.length === 0) {
      SLMessage.warning('请点击管线位置');
      state.curOperate = '';
      setMapCursor('');
      staticState.mapClick?.remove && staticState.mapClick.remove();
      return;
    }

    // 获取第一个查询结果
    const feature = res.data.features[0];
    console.log('选中的管线要素:', feature);

    // 保存选中的管线
    state.selectedPipe = {
      layerId: feature.id?.split('.')[0] || '管线图层',
      layerName: feature.id?.split('.')[0] || '管线图层',
      attributes: feature.properties,
      geometry: feature.geometry,
    };

    // 创建关阀点（使用点击位置，EPSG:3857坐标系）
    const burstPoint = new Point({
      x: e.mapPoint.x, // 直接使用EPSG:3857坐标系的x
      y: e.mapPoint.y, // 直接使用EPSG:3857坐标系的y
      spatialReference: { wkid: 3857 }, // 明确指定EPSG:3857坐标系
    });

    staticState.burstPoint = burstPoint;

    // 在地图上显示选中的管线和关阀点
    const pipeGraphic = new Graphic({
      geometry: new Polyline({
        paths: [feature.geometry.coordinates],
        spatialReference: view?.spatialReference,
      }),
      symbol: setSymbol('polyline', {
        color: '#FF0000',
        width: 3,
      }),
      attributes: feature.properties,
    });

    // 改进关阀点样式 - 使用更明显的爆炸样式
    const burstGraphic = new Graphic({
      geometry: burstPoint,
      symbol: setSymbol('point', {
        color: '#FF0000',
        size: 20,
        outlineColor: '#FFFFFF',
        outlineWidth: 4,
        style: 'circle',
      }),
      attributes: {
        ...feature.properties,
        type: 'burst_point',
      },
    });

    // 添加一个更大的外圈来突出关阀点
    const burstOuterGraphic = new Graphic({
      geometry: burstPoint,
      symbol: setSymbol('point', {
        color: 'rgba(255, 0, 0, 0.3)',
        size: 32,
        outlineColor: '#FF0000',
        outlineWidth: 2,
        style: 'circle',
      }),
      attributes: {
        type: 'burst_outer',
      },
    });

    staticState.markLayer?.add(pipeGraphic);
    staticState.markLayer?.add(burstGraphic);
    staticState.markLayer?.add(burstOuterGraphic);

    // 定位到选中区域
    const extent = pipeGraphic.geometry.extent;
    if (extent) {
      const width = extent.xmax - extent.xmin;
      const height = extent.ymax - extent.ymin;
      const xmin = extent.xmin - width / 2;
      const xmax = extent.xmax + width / 2;
      const ymin = extent.ymin - height / 2;
      const ymax = extent.ymax + height / 2;

      view?.goTo(
        new Extent({
          xmin,
          ymin,
          xmax,
          ymax,
          spatialReference: view.spatialReference,
        }),
      );
    }

    console.log('关阀点选择成功:', {
      selectedPipe: state.selectedPipe,
      burstPoint: staticState.burstPoint,
    });

    // 设置正确的状态
    state.curOperate = 'picked';
    setMapCursor('');
    staticState.mapClick?.remove && staticState.mapClick.remove();
    staticState.mapClick = undefined;

    // 强制更新表单显示
    await nextTick();
    // refForm.value?.refresh();
  } catch (error) {
    console.error('关阀点识别失败:', error);
    SLMessage.error('关阀点识别失败');
    state.curOperate = '';
    setMapCursor('');
    staticState.mapClick?.remove && staticState.mapClick.remove();
  }
};

const startAnalys = async () => {
  if (!staticState.view || !staticState.burstPoint) {
    SLMessage.warning('请先选择关阀点');
    return;
  }

  state.curOperate = 'analysing';

  try {
    // 清除之前的结果
    staticState.resultLayer && staticState.view?.map.remove(staticState.resultLayer);

    // 调用关阀分析接口
    const analysisParams = [
      {
        x: staticState.burstPoint.x, // 使用EPSG:3857坐标系的x
        y: staticState.burstPoint.y, // 使用EPSG:3857坐标系的y
      },
    ];

    const response = await burstpipeAnalysis(analysisParams);

    if (response?.success) {
      // 处理分析结果
      const resultData = response.data;
      // 解析新的返回结构
      const valveAnalysisResults = {
        initialValves: resultData['初始关闭阀门'] || [],
        initialPipes: resultData['初始受影响管线'] || [],
        secondaryValves: resultData['二次关闭阀门'] || [],
        secondaryPipes: resultData['二次关阀受影响管线'] || [],
      };

      // 更新状态
      state.valveAnalysisResults = valveAnalysisResults;

      // 根据返回结果查询具体要素并更新详细信息
      await queryAndRenderBurstResults(valveAnalysisResults);

      // 更新统计信息（仅用于UI显示统计数据）
      updateAnalysisSummary(valveAnalysisResults);

      SLMessage.success('关阀分析完成');
    } else {
      SLMessage.error(response?.data || '分析失败');
    }
  } catch (error) {
    console.error('关阀分析失败:', error);
    SLMessage.error('分析失败，请重试');
  }

  state.curOperate = 'analysed';
};

// 更新分析统计信息（仅用于UI显示）
const updateAnalysisSummary = (valveAnalysisResults: any) => {
  if (!valveAnalysisResults) {
    return;
  }

  // 统计各类型要素数量
  const summary: any = {
    totalValves:
      (valveAnalysisResults.initialValves?.length || 0) +
      (valveAnalysisResults.secondaryValves?.length || 0),
    totalPipes:
      (valveAnalysisResults.initialPipes?.length || 0) +
      (valveAnalysisResults.secondaryPipes?.length || 0),
    layersummary: [],
  };

  // 计算初始受影响管线总长度
  const initialPipesLength = staticState.queriedFeatures.pipes
    .filter(pipe => pipe.analysisType === 'initial')
    .reduce((total, pipe) => {
      const pipeLength = parseFloat(pipe.properties?.['管长'] || pipe.properties?.管长 || 0);
      return total + (isNaN(pipeLength) ? 0 : pipeLength);
    }, 0);

  // 计算二次关阀受影响管线总长度
  const secondaryPipesLength = staticState.queriedFeatures.pipes
    .filter(pipe => pipe.analysisType === 'secondary')
    .reduce((total, pipe) => {
      const pipeLength = parseFloat(pipe.properties?.['管长'] || pipe.properties?.管长 || 0);
      return total + (isNaN(pipeLength) ? 0 : pipeLength);
    }, 0);

  // 按类型分组统计
  const layerGroups: Array<{
    layername: string;
    count: number;
    geometrytype: string;
  }> = [
      {
        layername: '初始关闭阀门',
        count: valveAnalysisResults.initialValves?.length || 0,
        geometrytype: 'esriGeometryPoint',
      },
      {
        layername: '初始受影响管线',
        count: Math.round(initialPipesLength), // 使用实际管长，四舍五入到整数
        geometrytype: 'esriGeometryPolyline',
      },
      {
        layername: '二次关闭阀门',
        count: valveAnalysisResults.secondaryValves?.length || 0,
        geometrytype: 'esriGeometryPoint',
      },
      {
        layername: '二次关阀受影响管线',
        count: Math.round(secondaryPipesLength), // 使用实际管长，四舍五入到整数
        geometrytype: 'esriGeometryPolyline',
      },
    ];

  summary.layersummary = layerGroups.filter(item => item.count > 0);
  staticState.resultSummary = summary;

  // 更新state.analysisResults用于显示统计
  state.analysisResults = summary.layersummary.map((item: any) => ({
    layerName: item.layername,
    count: item.count,
    geometryType: item.geometrytype,
  }));

  // 注意：此方法已废弃，详细信息展示统一使用 refreshDetailWithQueriedData() 方法
};

// 使用实际查询到的要素数据更新详细信息（统一的详细信息更新入口）
const refreshDetailWithQueriedData = () => {
  const detailTabs: Array<{
    label: string;
    name: string;
    data?: any[];
  }> = [];

  // 按分析类型分组阀门数据
  const initialValves = staticState.queriedFeatures.valves.filter(
    v => v.analysisType === 'initial',
  );
  const secondaryValves = staticState.queriedFeatures.valves.filter(
    v => v.analysisType === 'secondary',
  );

  // 按分析类型分组管线数据
  const initialPipes = staticState.queriedFeatures.pipes.filter(p => p.analysisType === 'initial');
  const secondaryPipes = staticState.queriedFeatures.pipes.filter(
    p => p.analysisType === 'secondary',
  );

  // 添加初始关闭阀门
  if (initialValves.length > 0) {
    detailTabs.push({
      label: '初始关闭阀门',
      name: 'initialValves',
      data: initialValves.map((valve, index) => ({
        properties: {
          id: valve.id,
          ...valve.properties,
        },
        geometry: valve.geometry,
      })),
    });
  }

  // 添加初始受影响管线
  if (initialPipes.length > 0) {
    detailTabs.push({
      label: '初始受影响管线',
      name: 'initialPipes',
      data: initialPipes.map((pipe, index) => ({
        properties: {
          id: pipe.id,
          ...pipe.properties,
        },
        geometry: pipe.geometry,
      })),
    });
  }

  // 添加二次关闭阀门
  if (secondaryValves.length > 0) {
    detailTabs.push({
      label: '二次关闭阀门',
      name: 'secondaryValves',
      data: secondaryValves.map((valve, index) => ({
        properties: {
          id: valve.id,
          ...valve.properties,
        },
        geometry: valve.geometry,
      })),
    });
  }

  // 添加二次关阀受影响管线
  if (secondaryPipes.length > 0) {
    detailTabs.push({
      label: '二次关阀受影响管线',
      name: 'secondaryPipes',
      data: secondaryPipes.map((pipe, index) => ({
        properties: {
          id: pipe.id,
          ...pipe.properties,
        },
        geometry: pipe.geometry,
      })),
    });
  }

  // 统一的详细信息更新入口
  if (refMap.value?.refreshDetail) {
    console.log('更新详细信息，实际查询到的要素数据:', detailTabs);
    refMap.value.refreshDetail(detailTabs);
  }
};

const clear = () => {
  destroy();
  state.analysisResults = [];
  state.valveAnalysisResults = undefined;
  state.selectedPipe = undefined;

  // 清空查询到的要素数据
  staticState.queriedFeatures.valves = [];
  staticState.queriedFeatures.pipes = [];

  // 清空详细数据展示
  refreshDetailWithQueriedData();
};

const destroy = () => {
  setMapCursor('');
  staticState.resultLayer && staticState.view?.map.remove(staticState.resultLayer);
  staticState.markLayer && staticState.view?.map.remove(staticState.markLayer);
  staticState.mapClick?.remove && staticState.mapClick.remove();
};

onBeforeUnmount(() => {
  clear();
});

// 根据返回结果查询并渲染关阀分析结果
const queryAndRenderBurstResults = async (valveAnalysisResults: any) => {
  if (!staticState.view || !valveAnalysisResults) {
    return;
  }

  console.log('开始查询并渲染关阀分析结果:', valveAnalysisResults);

  // 清空之前的查询结果
  staticState.queriedFeatures.valves = [];
  staticState.queriedFeatures.pipes = [];

  // 创建结果图层
  staticState.resultLayer = getGraphicLayer(staticState.view, {
    id: 'burst-result',
    title: '关阀分析结果',
  });

  // 查询初始关闭阀门
  if (valveAnalysisResults.initialValves && valveAnalysisResults.initialValves.length > 0) {
    console.log('查询初始关闭阀门:', valveAnalysisResults.initialValves);
    await queryAndRenderValves(valveAnalysisResults.initialValves, 'initial');
  }

  // 查询初始受影响管线
  if (valveAnalysisResults.initialPipes && valveAnalysisResults.initialPipes.length > 0) {
    console.log('查询初始受影响管线:', valveAnalysisResults.initialPipes);
    await queryAndRenderPipes(valveAnalysisResults.initialPipes, 'initial');
  }

  // 查询二次关闭阀门
  if (valveAnalysisResults.secondaryValves && valveAnalysisResults.secondaryValves.length > 0) {
    console.log('查询二次关闭阀门:', valveAnalysisResults.secondaryValves);
    await queryAndRenderValves(valveAnalysisResults.secondaryValves, 'secondary');
  }

  // 查询二次关阀受影响管线
  if (valveAnalysisResults.secondaryPipes && valveAnalysisResults.secondaryPipes.length > 0) {
    console.log('查询二次关阀受影响管线:', valveAnalysisResults.secondaryPipes);
    await queryAndRenderPipes(valveAnalysisResults.secondaryPipes, 'secondary');
  }

  // 定位到结果区域
  if (staticState.resultLayer && staticState.resultLayer.graphics.length > 0) {
    const firstGraphic = staticState.resultLayer.graphics.getItemAt(0);
    if (firstGraphic && firstGraphic.geometry) {
      staticState.view?.goTo(firstGraphic.geometry, {
        duration: 1000,
      });
    }
  }

  // 查询完成后，使用实际查询到的要素数据更新详细信息
  refreshDetailWithQueriedData();
};

// 查询并渲染阀门
const queryAndRenderValves = async (
  valveIds: string[],
  type: 'initial' | 'upstream' | 'downstream' | 'secondary' = 'initial',
) => {
  if (!staticState.view || !staticState.resultLayer) {
    return;
  }

  try {
    // 构建查询条件 - 使用井编号字段，用双引号包围中文字段名
    const valveCondition = valveIds.map(id => `'${id}'`).join(',');
    // 用双引号包围中文字段名
    const whereClause = `"井编号" IN (${valveCondition})`;

    console.log('阀门查询条件:', whereClause);
    console.log('字段名格式: "井编号"');

    // 查询阀门图层（测流井图层）
    const valveResponse = await QueryByPolygon(
      'guazhou:测流井', // 图层名称
      null, // 不使用空间过滤
      whereClause, // 属性过滤条件
    );

    console.log('阀门查询结果:', valveResponse);

    if (valveResponse.data && valveResponse.data.features) {
      valveResponse.data.features.forEach((feature: any) => {
        if (feature.geometry && feature.geometry.type === 'Point') {
          // 保存查询到的要素数据
          staticState.queriedFeatures.valves.push({
            ...feature,
            analysisType: type,
            layerName: '测流井',
          });

          const geometry = new Point({
            x: feature.geometry.coordinates[0],
            y: feature.geometry.coordinates[1],
            spatialReference: { wkid: 3857 },
          });

          // 根据类型设置不同的颜色
          let valveColor = '#FF6B35'; // 默认橙红色
          let valveSize = 16;

          switch (type) {
            case 'initial':
              valveColor = '#FF6B35'; // 橙红色
              valveSize = 16;
              break;
            case 'upstream':
              valveColor = '#4CAF50'; // 绿色
              valveSize = 14;
              break;
            case 'downstream':
              valveColor = '#2196F3'; // 蓝色
              valveSize = 14;
              break;
            case 'secondary':
              valveColor = '#9C27B0'; // 紫色
              valveSize = 14;
              break;
          }

          // 创建阀门图形 - 使用更美观的样式
          const valveGraphic = new Graphic({
            geometry: geometry,
            symbol: setSymbol('point', {
              color: valveColor,
              size: valveSize,
              outlineColor: '#FFFFFF',
              outlineWidth: 3,
              style: 'circle',
            }),
            attributes: {
              ...feature.properties,
              type: 'valve',
              valveType: type,
              featureId: feature.id,
            },
          });

          // 创建阀门外圈 - 增加视觉层次
          const valveOuterGraphic = new Graphic({
            geometry: geometry,
            symbol: setSymbol('point', {
              color: `${valveColor}33`, // 半透明
              size: 28,
              outlineColor: valveColor,
              outlineWidth: 2,
              style: 'circle',
            }),
            attributes: {
              type: 'valve_outer',
              valveType: type,
              featureId: feature.id,
            },
          });

          // 创建阀门图标 - 使用菱形样式表示阀门
          const valveIconGraphic = new Graphic({
            geometry: geometry,
            symbol: setSymbol('point', {
              color: valveColor,
              size: 8,
              outlineColor: '#FFFFFF',
              outlineWidth: 1,
              style: 'diamond', // 菱形样式
            }),
            attributes: {
              type: 'valve_icon',
              valveType: type,
              featureId: feature.id,
            },
          });

          (staticState.resultLayer as __esri.GraphicsLayer)?.add(valveOuterGraphic);
          (staticState.resultLayer as __esri.GraphicsLayer)?.add(valveGraphic);
          (staticState.resultLayer as __esri.GraphicsLayer)?.add(valveIconGraphic);
        }
      });
    }
  } catch (error) {
    console.error('查询阀门失败:', error);
    // 如果双引号方法失败，尝试使用OBJECTID查询
    console.log('尝试使用OBJECTID查询阀门...');
    await queryAndRenderValvesByObjectId(valveIds, type);
  }
};

// 备用方法：使用OBJECTID查询阀门
const queryAndRenderValvesByObjectId = async (
  valveIds: string[],
  type: 'initial' | 'upstream' | 'downstream' | 'secondary' = 'initial',
) => {
  if (!staticState.view || !staticState.resultLayer) {
    return;
  }

  try {
    // 尝试使用OBJECTID字段查询
    const valveCondition = valveIds.map(id => `'${id}'`).join(',');
    const whereClause = `OBJECTID IN (${valveCondition})`;

    console.log('备用阀门查询条件 (OBJECTID):', whereClause);

    const valveResponse = await QueryByPolygon('guazhou:测流井', null, whereClause);

    console.log('备用阀门查询结果:', valveResponse);

    if (valveResponse.data && valveResponse.data.features) {
      valveResponse.data.features.forEach((feature: any) => {
        if (feature.geometry && feature.geometry.type === 'Point') {
          // 保存查询到的要素数据
          staticState.queriedFeatures.valves.push({
            ...feature,
            analysisType: type,
            layerName: '测流井',
          });

          const geometry = new Point({
            x: feature.geometry.coordinates[0],
            y: feature.geometry.coordinates[1],
            spatialReference: { wkid: 3857 },
          });

          const graphic = new Graphic({
            geometry: geometry,
            symbol: setSymbol('point', {
              color: '#FF6B35', // 橙红色
              size: 16,
              outlineColor: '#FFFFFF',
              outlineWidth: 3,
              style: 'circle',
            }),
            attributes: {
              ...feature.properties,
              type: 'valve',
              featureId: feature.id,
            },
          });

          (staticState.resultLayer as __esri.GraphicsLayer)?.add(graphic);
        }
      });
    }
  } catch (error) {
    console.error('备用阀门查询也失败:', error);
  }
};

// 查询并渲染管线
const queryAndRenderPipes = async (
  pipeIds: string[],
  type: 'initial' | 'upstream' | 'downstream' | 'secondary' = 'initial',
) => {
  if (!staticState.view || !staticState.resultLayer) {
    return;
  }

  try {
    // 构建查询条件 - 使用测点编号字段，用双引号包围中文字段名
    const pipeCondition = pipeIds.map(id => `'${id}'`).join(',');
    // 用双引号包围中文字段名
    const whereClause = `"测点编号" IN (${pipeCondition})`;

    console.log('管线查询条件:', whereClause);
    console.log('字段名格式: "测点编号"');

    // 查询管线图层
    const pipeResponse = await QueryByPolygon(
      'guazhou:管线', // 图层名称
      null, // 不使用空间过滤
      whereClause, // 属性过滤条件
    );

    console.log('管线查询结果:', pipeResponse);

    if (pipeResponse.data && pipeResponse.data.features) {
      pipeResponse.data.features.forEach((feature: any) => {
        if (feature.geometry && feature.geometry.type === 'LineString') {
          // 保存查询到的要素数据
          staticState.queriedFeatures.pipes.push({
            ...feature,
            analysisType: type,
            layerName: '管线',
          });

          const geometry = new Polyline({
            paths: [feature.geometry.coordinates],
            spatialReference: { wkid: 3857 },
          });

          // 根据类型设置不同的颜色
          let pipeColor = '#FF6B6B'; // 默认红色
          let pipeWidth = 4;

          switch (type) {
            case 'initial':
              pipeColor = '#FF6B6B'; // 红色
              pipeWidth = 4;
              break;
            case 'upstream':
              pipeColor = '#4CAF50'; // 绿色
              pipeWidth = 3;
              break;
            case 'downstream':
              pipeColor = '#2196F3'; // 蓝色
              pipeWidth = 3;
              break;
            case 'secondary':
              pipeColor = '#9C27B0'; // 紫色
              pipeWidth = 3;
              break;
          }

          const graphic = new Graphic({
            geometry: geometry,
            symbol: setSymbol('polyline', {
              color: pipeColor,
              width: pipeWidth,
            }),
            attributes: {
              ...feature.properties,
              type: 'pipe',
              pipeType: type,
              featureId: feature.id,
            },
          });

          (staticState.resultLayer as __esri.GraphicsLayer)?.add(graphic);
        }
      });
    }
  } catch (error) {
    console.error('查询管线失败:', error);
    // 如果双引号方法失败，尝试使用OBJECTID查询
    console.log('尝试使用OBJECTID查询管线...');
    await queryAndRenderPipesByObjectId(pipeIds, type);
  }
};

// 备用方法：使用OBJECTID查询管线
const queryAndRenderPipesByObjectId = async (
  pipeIds: string[],
  type: 'initial' | 'upstream' | 'downstream' | 'secondary' = 'initial',
) => {
  if (!staticState.view || !staticState.resultLayer) {
    return;
  }

  try {
    // 尝试使用OBJECTID字段查询
    const pipeCondition = pipeIds.map(id => `'${id}'`).join(',');
    const whereClause = `OBJECTID IN (${pipeCondition})`;

    console.log('备用管线查询条件 (OBJECTID):', whereClause);

    const pipeResponse = await QueryByPolygon('guazhou:管线', null, whereClause);

    console.log('备用管线查询结果:', pipeResponse);

    if (pipeResponse.data && pipeResponse.data.features) {
      pipeResponse.data.features.forEach((feature: any) => {
        if (feature.geometry && feature.geometry.type === 'LineString') {
          // 保存查询到的要素数据
          staticState.queriedFeatures.pipes.push({
            ...feature,
            analysisType: type,
            layerName: '管线',
          });

          const geometry = new Polyline({
            paths: [feature.geometry.coordinates],
            spatialReference: { wkid: 3857 },
          });

          const graphic = new Graphic({
            geometry: geometry,
            symbol: setSymbol('polyline', {
              color: '#FF6B6B',
              width: 4,
            }),
            attributes: {
              ...feature.properties,
              type: 'pipe',
              featureId: feature.id,
            },
          });

          (staticState.resultLayer as __esri.GraphicsLayer)?.add(graphic);
        }
      });
    }
  } catch (error) {
    console.error('备用管线查询也失败:', error);
  }
};

// 根据图层名称获取颜色
const getLayerColor = (layerName: string) => {
  // 根据具体的图层名称返回对应的渲染颜色
  switch (layerName) {
    case '初始关闭阀门':
      return '#FF6B35'; // 橙红色，与渲染时的initial阀门颜色一致
    case '二次关闭阀门':
      return '#9C27B0'; // 紫色，与渲染时的secondary阀门颜色一致
    case '初始受影响管线':
      return '#FF6B6B'; // 红色，与渲染时的initial管线颜色一致
    case '二次关阀受影响管线':
      return '#9C27B0'; // 紫色，与渲染时的secondary管线颜色一致
    default:
      // 为其他图层提供默认颜色
      if (layerName.includes('阀门') || layerName.includes('测流井')) {
        return '#FF6B35'; // 阀门专用橙色
      }
      if (layerName.includes('管线')) {
        return '#FF6B6B'; // 管线专用红色
      }
      // 简单的哈希算法生成颜色
      const LAYER_COLORS = [
        '#FF6B35', // 阀门专用橙色
        '#FF0000',
        '#00FF00',
        '#0000FF',
        '#FFFF00',
        '#FF00FF',
        '#00FFFF',
        '#FF8000',
        '#8000FF',
        '#0080FF',
        '#FF0080',
        '#80FF00',
        '#0080FF',
      ];
      let hash = 0;
      for (let i = 0; i < layerName.length; i++) {
        hash = layerName.charCodeAt(i) + ((hash << 5) - hash);
      }
      const index = Math.abs(hash) % LAYER_COLORS.length;
      return LAYER_COLORS[index];
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;

  .el-table__empty-text {
    line-height: 40px;
  }
}

.burst-stats {
  margin: 10px 0;
  padding: 12px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;

  .stats-title {
    font-size: 14px;
    font-weight: 600;
    color: #856404;
    margin-bottom: 8px;
  }

  .stats-content {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 8px;
  }

  .stats-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    padding: 4px 8px;
    background-color: #ffffff;
    border-radius: 4px;
    border: 1px solid #ffeaa7;

    .color-box {
      width: 16px;
      height: 16px;
      border: 1px solid #ccc;
      border-radius: 2px;
      margin-right: 6px;
    }

    .layer-label {
      color: #856404;
      font-weight: 500;
      margin-right: 4px;
    }

    .layer-count {
      color: #333;
      font-weight: 600;
    }
  }

  .stats-summary {
    font-size: 12px;
    color: #856404;
    font-weight: 600;
    border-top: 1px solid #ffeaa7;
    padding-top: 6px;
  }
}
</style>
