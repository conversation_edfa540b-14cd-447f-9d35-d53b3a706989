<template>
  <RightDrawerMap ref="refMap" :title="'水龄分析'" @map-loaded="onMapLoaded">
    <Form ref="refForm" :config="FormConfig" />

    <!-- 水龄分析结果汇总 -->
    <div v-if="state.analysisResults.length > 0" class="water-age-stats">
      <div class="stats-title">水龄分析统计</div>
      <div class="stats-content">
        <div v-for="stat in state.analysisResults" :key="stat.gradeName" class="stats-item">
          <div class="color-box" :style="{ backgroundColor: stat.color }" />
          <span class="grade-label">{{ stat.gradeName }}:</span>
          <span class="node-count">{{ stat.nodeCount }}个节点</span>
          <span class="percentage">({{ stat.percentage.toFixed(1) }}%)</span>
        </div>
      </div>
      <div class="stats-summary">
        <div>总节点数: {{ state.totalNodes }}个</div>
        <div>平均水龄: {{ state.averageAge.toFixed(2) }}小时</div>
        <div>最大水龄: {{ state.maxAge.toFixed(2) }}小时</div>
        <div v-if="state.abnormalNodes > 0" class="abnormal-warning">
          异常节点: {{ state.abnormalNodes }}个
        </div>
      </div>
    </div>

    <!-- 水龄分级配置 -->
    <div v-if="state.showGradeConfig" class="grade-config">
      <div class="config-title">水龄分级配置</div>
      <div v-for="(grade, index) in state.gradeConfigs" :key="index" class="grade-item">
        <div class="color-indicator" :style="{ backgroundColor: grade.color }"></div>
        <span class="grade-name">{{ grade.gradeName }}</span>
        <span class="age-range">{{ grade.minAge }}-{{ grade.maxAge }}小时</span>
        <span v-if="grade.abnormal" class="abnormal-tag">异常</span>
      </div>
    </div>
  </RightDrawerMap>
</template>

<script lang="ts" setup>
import { reactive, ref, onBeforeUnmount } from 'vue';
import Point from '@arcgis/core/geometry/Point';
import Graphic from '@arcgis/core/Graphic';
import { getGraphicLayer, setSymbol } from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import {
  runWaterAge,
  generateWaterAgeHeatmap,
  quickAnalyzeWaterAge,
  getDefaultWaterAge,
} from '@/api/waterModels/waterAge';

// @ts-ignore
import RightDrawerMap from '../common/RightDrawerMap.vue';

const refForm = ref<IFormIns>();
const refMap = ref<InstanceType<typeof RightDrawerMap>>();

defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();

// 状态管理
const state = reactive<{
  curOperate: 'configuring' | 'analyzing' | 'analyzed' | '';
  analysisResults: Array<{
    gradeName: string;
    nodeCount: number;
    percentage: number;
    color: string;
  }>;
  totalNodes: number;
  averageAge: number;
  maxAge: number;
  abnormalNodes: number;
  showGradeConfig: boolean;
  gradeConfigs: Array<{
    gradeName: string;
    minAge: number;
    maxAge: number;
    color: string;
    description: string;
    abnormal: boolean;
  }>;
}>({
  curOperate: '',
  analysisResults: [],
  totalNodes: 0,
  averageAge: 0,
  maxAge: 0,
  abnormalNodes: 0,
  showGradeConfig: false,
  gradeConfigs: [],
});

// 静态状态
const staticState: {
  view?: __esri.MapView;
  heatmapLayer?: __esri.GraphicsLayer;
  abnormalLayer?: __esri.GraphicsLayer;
} = {
  view: undefined,
  heatmapLayer: undefined,
  abnormalLayer: undefined,
};

// 地图加载完成回调
const onMapLoaded = (view: __esri.MapView) => {
  staticState.view = view;
  initDefaultGradeConfig();
};

// 表单配置
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '分析配置',
      },
      fields: [
        {
          type: 'input-number',
          label: '异常阈值(小时)',
          field: 'abnormalThreshold',
          prop: 'abnormalThreshold',
          placeholder: '请输入异常阈值',
          min: 0,
          max: 168,
        },
        {
          type: 'switch',
          label: '显示热力图',
          field: 'showHeatmap',
          prop: 'showHeatmap',
        },
        {
          type: 'switch',
          label: '标记异常节点',
          field: 'markAbnormal',
          prop: 'markAbnormal',
        },
        {
          type: 'switch',
          label: '显示分级配置',
          field: 'showGradeConfig',
          prop: 'showGradeConfig',
          onChange: (val: boolean) => {
            state.showGradeConfig = val;
          },
        },
      ],
    },
    {
      fieldset: {
        desc: '操作',
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: { width: '100%' },
              loading: () => state.curOperate === 'analyzing',
              text: () => (state.curOperate === 'analyzing' ? '正在分析' : '开始分析'),
              disabled: () => state.curOperate === 'analyzing',
              click: () => startAnalysis(),
            },
            {
              perm: true,
              styles: { width: '100%' },
              type: 'success',
              text: '快速分析',
              disabled: () => state.curOperate === 'analyzing',
              click: () => quickAnalysis(),
            },
            {
              perm: true,
              styles: { width: '100%' },
              type: 'warning',
              text: '配置分级',
              click: () => configureGrades(),
            },
            {
              perm: true,
              styles: { width: '100%' },
              type: 'danger',
              text: '清除结果',
              disabled: () => state.curOperate === 'analyzing',
              click: () => clearResults(),
            },
          ],
        },
      ],
    },
  ],
  defaultValue: {
    abnormalThreshold: 48,
    showHeatmap: true,
    markAbnormal: true,
    showGradeConfig: false,
  },
});

// 初始化默认分级配置
const initDefaultGradeConfig = async () => {
  try {
    const result = await getDefaultWaterAge();
    if (result.data?.success) {
      state.gradeConfigs = result.data.data || [];
    }
  } catch (error) {
    console.error('获取默认分级配置失败:', error);
    // 使用默认配置
    state.gradeConfigs = [
      {
        gradeName: '优秀',
        minAge: 0,
        maxAge: 12,
        color: '#00FF00',
        description: '水质优秀',
        abnormal: false,
      },
      {
        gradeName: '良好',
        minAge: 12,
        maxAge: 24,
        color: '#90EE90',
        description: '水质良好',
        abnormal: false,
      },
      {
        gradeName: '一般',
        minAge: 24,
        maxAge: 48,
        color: '#FFD700',
        description: '水质一般',
        abnormal: false,
      },
      {
        gradeName: '较差',
        minAge: 48,
        maxAge: 72,
        color: '#FFA500',
        description: '水质较差',
        abnormal: true,
      },
      {
        gradeName: '异常',
        minAge: 72,
        maxAge: 999,
        color: '#FF0000',
        description: '水质异常',
        abnormal: true,
      },
    ];
  }
};

// 开始分析
const startAnalysis = async () => {
  if (!staticState.view) {
    SLMessage.warning('地图未加载完成');
    return;
  }

  state.curOperate = 'analyzing';

  try {
    // 先运行水龄仿真
    const runResult = await runWaterAge({
      dt: 3600, // 1小时步长
      totalTime: 86400, // 24小时
    });

    if (!runResult.data?.success) {
      throw new Error(runResult.data?.message || '水龄仿真失败');
    }

    // 生成热力图和统计数据
    const formData = refForm.value?.formData || {};
    const params = {
      analysisTime: new Date().toISOString(),
      gradeConfigs: state.gradeConfigs,
      abnormalThreshold: formData.abnormalThreshold || 48,
      includeHeatmap: formData.showHeatmap || true,
      includeStatistics: true,
      markAbnormalNodes: formData.markAbnormal || true,
      regionGroupField: 'region',
      generateReport: false,
      exportFormat: 'PDF',
    };

    const heatmapResult = await generateWaterAgeHeatmap(params);

    if (heatmapResult.data?.success) {
      await renderAnalysisResults(heatmapResult.data.data);
      SLMessage.success('水龄分析完成');
    } else {
      throw new Error(heatmapResult.data?.message || '生成热力图失败');
    }
  } catch (error) {
    console.error('水龄分析失败:', error);
    SLMessage.error('分析失败，请重试');
  }

  state.curOperate = 'analyzed';
};

// 快速分析
const quickAnalysis = async () => {
  if (!staticState.view) {
    SLMessage.warning('地图未加载完成');
    return;
  }

  state.curOperate = 'analyzing';

  try {
    const formData = refForm.value?.formData || {};
    const params = {
      analysisTime: new Date().toISOString(),
      gradeConfigs: state.gradeConfigs,
      abnormalThreshold: formData.abnormalThreshold || 48,
      includeHeatmap: formData.showHeatmap || true,
      includeStatistics: true,
      markAbnormalNodes: formData.markAbnormal || true,
      regionGroupField: 'region',
      generateReport: false,
      exportFormat: 'PDF',
    };

    const result = await quickAnalyzeWaterAge(params);

    if (result.data?.success) {
      await renderAnalysisResults(result.data.data);
      SLMessage.success('快速分析完成');
    } else {
      throw new Error(result.data?.message || '快速分析失败');
    }
  } catch (error) {
    console.error('快速分析失败:', error);
    SLMessage.error('分析失败，请重试');
  }

  state.curOperate = 'analyzed';
};

// 渲染分析结果
const renderAnalysisResults = async (data: any) => {
  if (!staticState.view || !data) {
    return;
  }

  // 清除之前的结果
  clearResults();

  // 更新统计信息
  if (data.statistics) {
    const stats = data.statistics;
    state.totalNodes = stats.totalNodes || 0;
    state.averageAge = stats.averageAge || 0;
    state.maxAge = stats.maxAge || 0;
    state.abnormalNodes = stats.abnormalNodes?.length || 0;

    // 更新分级统计
    state.analysisResults = (stats.intervalStatistics || []).map((item: any) => ({
      gradeName: item.intervalName,
      nodeCount: item.nodeCount,
      percentage: item.percentage,
      color: state.gradeConfigs.find(g => g.gradeName === item.intervalName)?.color || '#999999',
    }));
  }

  // 渲染热力图
  if (data.heatmap?.nodeHeatmapPoints) {
    await renderHeatmap(data.heatmap.nodeHeatmapPoints);
  }

  // 标记异常节点
  if (data.statistics?.abnormalNodes) {
    await renderAbnormalNodes(data.statistics.abnormalNodes);
  }
};

// 渲染热力图
const renderHeatmap = async (heatmapPoints: any[]) => {
  if (!staticState.view) {
    return;
  }

  staticState.heatmapLayer = getGraphicLayer(staticState.view, {
    id: 'water-age-heatmap',
    title: '水龄热力图',
  });

  heatmapPoints.forEach((point: any) => {
    if (point.longitude && point.latitude) {
      const graphic = new Graphic({
        geometry: new Point({
          longitude: point.longitude,
          latitude: point.latitude,
        }),
        symbol: setSymbol('point', {
          color: point.color || '#999999',
          size: 12,
          outlineColor: '#FFFFFF',
          outlineWidth: 1,
          style: 'circle',
        }),
        attributes: {
          nodeId: point.nodeId,
          nodeName: point.nodeName,
          ageValue: point.ageValue,
          ageGrade: point.ageGrade,
        },
      });

      staticState.heatmapLayer?.add(graphic);
    }
  });
};

// 渲染异常节点
const renderAbnormalNodes = async (abnormalNodes: any[]) => {
  if (!staticState.view) {
    return;
  }

  staticState.abnormalLayer = getGraphicLayer(staticState.view, {
    id: 'water-age-abnormal',
    title: '异常节点',
  });

  abnormalNodes.forEach((node: any) => {
    if (node.longitude && node.latitude) {
      const graphic = new Graphic({
        geometry: new Point({
          longitude: node.longitude,
          latitude: node.latitude,
        }),
        symbol: setSymbol('point', {
          color: '#FF0000',
          size: 16,
          outlineColor: '#FFFFFF',
          outlineWidth: 2,
          style: 'triangle',
        }),
        attributes: {
          nodeId: node.nodeId,
          nodeName: node.nodeName,
          ageValue: node.ageValue,
          type: 'abnormal',
        },
      });

      staticState.abnormalLayer?.add(graphic);
    }
  });
};

// 配置分级
const configureGrades = () => {
  state.showGradeConfigDialog = true;
};

// 保存分级配置
const onGradeConfigSave = (configs: any[]) => {
  state.gradeConfigs = configs;
  SLMessage.success('分级配置已更新');
};

// 清除结果
const clearResults = () => {
  if (staticState.heatmapLayer) {
    staticState.view?.map.remove(staticState.heatmapLayer);
    staticState.heatmapLayer = undefined;
  }

  if (staticState.abnormalLayer) {
    staticState.view?.map.remove(staticState.abnormalLayer);
    staticState.abnormalLayer = undefined;
  }

  state.analysisResults = [];
  state.totalNodes = 0;
  state.averageAge = 0;
  state.maxAge = 0;
  state.abnormalNodes = 0;
  state.curOperate = '';
};

// 组件卸载时清理
onBeforeUnmount(() => {
  clearResults();
});
</script>

<style lang="scss" scoped>
.water-age-stats {
  margin: 10px 0;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;

  .stats-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 8px;
  }

  .stats-content {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
  }

  .stats-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    padding: 4px 8px;
    background-color: #ffffff;
    border-radius: 4px;
    border: 1px solid #bfdbfe;

    .color-box {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 4px;
    }

    .grade-label {
      color: #1e40af;
      font-weight: 500;
      margin-right: 4px;
    }

    .node-count {
      color: #333;
      font-weight: 600;
      margin-right: 4px;
    }

    .percentage {
      color: #666;
      font-size: 11px;
    }
  }

  .stats-summary {
    font-size: 12px;
    color: #1e40af;
    border-top: 1px solid #bfdbfe;
    padding-top: 6px;

    div {
      margin-bottom: 2px;
    }

    .abnormal-warning {
      color: #dc2626;
      font-weight: 600;
    }
  }
}

.grade-config {
  margin: 10px 0;
  padding: 12px;
  background-color: #fefce8;
  border: 1px solid #fde047;
  border-radius: 6px;

  .config-title {
    font-size: 14px;
    font-weight: 600;
    color: #a16207;
    margin-bottom: 8px;
  }

  .grade-item {
    display: flex;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;

    .color-indicator {
      width: 16px;
      height: 16px;
      border-radius: 2px;
      margin-right: 8px;
      border: 1px solid #ccc;
    }

    .grade-name {
      font-weight: 500;
      margin-right: 8px;
      min-width: 40px;
    }

    .age-range {
      color: #666;
      margin-right: 8px;
    }

    .abnormal-tag {
      background-color: #fee2e2;
      color: #dc2626;
      padding: 1px 4px;
      border-radius: 2px;
      font-size: 10px;
    }
  }
}
</style>
