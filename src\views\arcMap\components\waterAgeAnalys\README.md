# 水龄模拟分析功能模块

## 功能概述

水龄模拟分析功能模块围绕管网水龄数据提供全流程分析与展示服务，包括：

- 水龄历史数据查询功能
- 水龄分布热力图生成功能
- 水龄分级区间自定义功能
- 水龄区间节点数量占比统计
- 水龄异常节点标记功能
- 区域水龄平均值统计对比功能
- 水龄分析报告生成和导出功能

## 文件结构

```
src/views/arcMap/components/waterAgeAnalys/
├── index.vue          # 水龄分析台账页面
├── MapAnalys.vue      # 水龄地图分析页面
├── GradeConfig.vue    # 水龄分级配置组件
├── data.ts           # 数据配置文件
└── README.md         # 说明文档
```

## 主要功能

### 1. 水龄分析台账页面 (index.vue)

**功能特性：**
- 水龄历史数据查询和筛选
- 支持按节点ID、管道ID、时间范围、水龄阈值等条件查询
- 表格展示水龄数据，包括节点信息、水龄值、分级等
- 支持导出水龄分析报告（PDF格式）
- 动态加载节点和管道选项

**主要API接口：**
- `queryWaterAgeHistory()` - 查询水龄历史数据
- `exportWaterAgeReport()` - 导出水龄分析报告
- `getAllNodes()` - 获取所有节点列表
- `getAllPipes()` - 获取所有管道列表

### 2. 水龄地图分析页面 (MapAnalys.vue)

**功能特性：**
- 水龄仿真运行和快速分析
- 水龄分布热力图可视化展示
- 异常节点标记和高亮显示
- 水龄统计信息实时展示（总节点数、平均水龄、最大水龄、异常节点数）
- 水龄分级配置管理
- 分析结果清除功能

**主要API接口：**
- `runWaterAge()` - 运行水龄仿真
- `quickAnalyzeWaterAge()` - 快速分析
- `generateWaterAgeHeatmap()` - 生成热力图
- `getDefaultWaterAge()` - 获取默认分级配置

**分析配置选项：**
- 异常阈值设置（小时）
- 显示热力图开关
- 标记异常节点开关
- 显示分级配置开关

### 3. 水龄分级配置组件 (GradeConfig.vue)

**功能特性：**
- 自定义水龄分级区间
- 分级名称、水龄范围、颜色、描述配置
- 异常标记设置
- 配置验证（范围重叠检查、名称重复检查等）
- 预览效果展示
- 重置为默认配置

**主要API接口：**
- `setWaterAge()` - 保存分级配置
- `getDefaultWaterAge()` - 获取默认配置

### 4. 数据配置文件 (data.ts)

**包含配置：**
- 水龄等级选项 (`waterAgeGrades`)
- 分析类型选项 (`analysisTypes`)
- 区域选项 (`regionOptions`)
- 搜索表单字段配置 (`moreFormDataFields`)
- 表格列配置 (`accountColumns`)
- 导出字段映射 (`waterAgeExportMapping`)

## 技术实现

### 前端技术栈
- Vue 3 + TypeScript
- Element Plus UI组件库
- ArcGIS JavaScript API (地图可视化)
- Day.js (时间处理)

### 地图可视化
- 使用ArcGIS GraphicsLayer渲染热力图点
- 支持不同颜色和符号标记节点
- 异常节点使用三角形符号突出显示
- 支持图层管理和清除

### 状态管理
- 使用Vue 3 Composition API
- 响应式状态管理分析结果和配置
- 静态状态管理地图图层引用

## API接口说明

### 核心分析接口

1. **运行水龄仿真**
   ```typescript
   runWaterAge({
     dt: 3600,        // 时间步长（秒）
     totalTime: 86400 // 总时间（秒）
   })
   ```

2. **快速分析**
   ```typescript
   quickAnalyzeWaterAge({
     analysisTime: string,
     gradeConfigs: GradeConfig[],
     abnormalThreshold: number,
     includeHeatmap: boolean,
     includeStatistics: boolean,
     markAbnormalNodes: boolean,
     regionGroupField: 'region',
     generateReport: boolean,
     exportFormat: 'PDF'
   })
   ```

3. **生成热力图**
   ```typescript
   generateWaterAgeHeatmap(params)
   ```

### 数据查询接口

1. **查询历史数据**
   ```typescript
   queryWaterAgeHistory({
     startTime: string,
     endTime: string,
     nodeIds: number[],
     pipeIds: number[],
     ageThreshold: number
   })
   ```

2. **导出报告**
   ```typescript
   exportWaterAgeReport(params) // 返回PDF blob
   ```

### 配置管理接口

1. **获取默认配置**
   ```typescript
   getDefaultWaterAge()
   ```

2. **保存配置**
   ```typescript
   setWaterAge(gradeConfigs)
   ```

## 使用说明

### 台账页面使用
1. 打开水龄分析台账页面
2. 设置查询条件（时间范围、节点、管道等）
3. 点击查询按钮获取数据
4. 查看表格中的水龄数据
5. 点击导出按钮生成PDF报告

### 地图分析使用
1. 打开水龄地图分析页面
2. 配置分析参数（异常阈值、显示选项等）
3. 点击"开始分析"进行完整分析，或"快速分析"获取快速结果
4. 查看地图上的热力图和统计信息
5. 可通过"配置分级"自定义水龄等级
6. 使用"清除结果"清理分析结果

### 分级配置使用
1. 在地图分析页面点击"配置分级"
2. 添加、编辑或删除分级配置
3. 设置分级名称、水龄范围、颜色等
4. 预览配置效果
5. 保存配置应用到分析中

## 注意事项

1. **地图依赖**：地图分析功能需要ArcGIS地图服务正常运行
2. **API依赖**：所有功能依赖后端水龄分析API接口
3. **数据格式**：确保API返回数据格式与前端期望一致
4. **性能考虑**：大量节点的热力图渲染可能影响性能
5. **错误处理**：已实现基本错误处理和用户提示

## 扩展功能

未来可考虑添加的功能：
- 水龄趋势分析图表
- 更多导出格式支持（Excel、CSV等）
- 实时水龄监控
- 水龄预警系统
- 更丰富的地图交互功能
