import request from '@/plugins/axios';

// 运行关阀仿真
export function runCloseValve(params: {
  actions: [
    {
      valveId: number;
      closeTime: number;
      closeDuration: number;
      linear: boolean;
      closeType: string;
    },
  ];
  nx: number;
  dt: number;
  steps: number;
}) {
  return request({
    url: '/waterModels/water/valve/run',
    method: 'post',
    data: params,
  });
  // 返回数据示例
  // {
  //   "csvPath": "string",
  //   "impacts": [
  //     {
  //       "pipeId": 0,
  //       "type": "string",
  //       "description": "string",
  //       "value": 0,
  //       "time": 0
  //     }
  //   ]
  // }
}

// 获取阀门状态信息
export function getValveStatus(valveId: number) {
  return request({
    url: `/waterModels/water/valve/status/${valveId}`,
    method: 'get',
  });
}

// 批量获取阀门状态
export function getBatchValveStatus(valveIds: number[]) {
  return request({
    url: '/waterModels/water/valve/batch-status',
    method: 'post',
    data: { valveIds },
  });
}

// 设置阀门状态
export function setValveStatus(
  valveId: number,
  status: 'open' | 'closed' | 'partial',
  openingDegree?: number,
) {
  return request({
    url: `/waterModels/water/valve/set-status/${valveId}`,
    method: 'post',
    data: { status, openingDegree },
  });
}

// 获取关阀影响分析
export function getCloseValveImpact(valveIds: number[]) {
  return request({
    url: '/waterModels/water/valve/impact-analysis',
    method: 'post',
    data: { valveIds },
  });
}
