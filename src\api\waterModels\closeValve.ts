import request from '@/plugins/axios';

// 运行关阀仿真
export function runCloseValve(params: {
  actions: [
    {
      valveId: number;
      closeTime: number;
      closeDuration: number;
      linear: boolean;
      closeType: string;
    },
  ];
  nx: number;
  dt: number;
  steps: number;
}) {
  return request({
    url: '/waterModels/water/valve/run',
    method: 'post',
    data: params,
  });
  // 返回数据示例
  // {
  //   "csvPath": "string",
  //   "impacts": [
  //     {
  //       "pipeId": 0,
  //       "type": "string",
  //       "description": "string",
  //       "value": 0,
  //       "time": 0
  //     }
  //   ]
  // }
}
