import dayjs from 'dayjs';
import * as XLSX from 'xlsx';

// 水龄等级选项
export const waterAgeGrades = [
  { label: '优秀', value: 'excellent' },
  { label: '良好', value: 'good' },
  { label: '一般', value: 'normal' },
  { label: '较差', value: 'poor' },
  { label: '异常', value: 'abnormal' },
];

// 分析类型选项
export const analysisTypes = [
  { label: '实时分析', value: 'realtime' },
  { label: '历史分析', value: 'history' },
  { label: '对比分析', value: 'compare' },
];

// 区域选项
export const regionOptions = [
  { label: '住宅区', value: 'residential' },
  { label: '商业区', value: 'commercial' },
  { label: '工业区', value: 'industrial' },
  { label: '主管网', value: 'main_network' },
];

// 水龄分析搜索表单字段
export const moreFormDataFields: any = [
  {
    type: 'select',
    label: '节点ID',
    field: 'nodeIds',
    prop: 'nodeIds',
    placeholder: '请选择节点ID',
    multiple: true,
    options: [], // 动态加载节点选项
    rules: [],
  },
  {
    type: 'select',
    label: '管道ID',
    field: 'pipeIds',
    prop: 'pipeIds',
    placeholder: '请选择管道ID',
    multiple: true,
    options: [], // 动态加载管道选项
    rules: [],
  },
  {
    type: 'input-number',
    label: '水龄阈值(小时)',
    field: 'ageThreshold',
    prop: 'ageThreshold',
    placeholder: '请输入水龄阈值',
    min: 0,
    max: 168, // 一周的小时数
    rules: [],
  },
  {
    type: 'daterange',
    label: '时间范围',
    field: 'timeRange',
    prop: 'timeRange',
    placeholder: ['开始时间', '结束时间'],
    rules: [],
  },
  {
    type: 'select',
    label: '分析类型',
    field: 'analysisType',
    prop: 'analysisType',
    placeholder: '请选择分析类型',
    options: analysisTypes,
    rules: [],
  },
  {
    type: 'select',
    label: '区域',
    field: 'region',
    prop: 'region',
    placeholder: '请选择区域',
    options: regionOptions,
    rules: [],
  },
];

// 水龄分析表格列配置
export const accountColumns: any = [
  { label: '节点ID', prop: 'nodeId', minWidth: 100 },
  { label: '节点名称', prop: 'nodeName', minWidth: 120 },
  { label: '管道ID', prop: 'pipeId', minWidth: 100 },
  { label: '管道名称', prop: 'pipeName', minWidth: 120 },
  { label: '水龄值(小时)', prop: 'ageValue', minWidth: 120 },
  { label: '水龄等级', prop: 'ageGrade', minWidth: 100 },
  { label: '区域', prop: 'region', minWidth: 100 },
  {
    label: '是否异常',
    prop: 'isAbnormal',
    minWidth: 100,
    formatter: (row: any, value: any) => (value ? '是' : '否'),
  },
  {
    label: '分析时间',
    prop: 'analysisTime',
    minWidth: 160,
    formatter: (row: any, value: any) => {
      return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    label: '创建时间',
    prop: 'createTime',
    minWidth: 160,
    formatter: (row: any, value: any) => {
      return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  { label: '备注', prop: 'remark', minWidth: 120 },
];

// 水龄分析Excel导出字段映射
export const waterAgeExportMapping = {
  节点ID: 'nodeId',
  节点名称: 'nodeName',
  管道ID: 'pipeId',
  管道名称: 'pipeName',
  水龄值: 'ageValue',
  水龄等级: 'ageGrade',
  区域: 'region',
  是否异常: 'isAbnormal',
  分析时间: 'analysisTime',
  创建时间: 'createTime',
  备注: 'remark',
};

// Excel读取工具函数
export const readExcelToJson = (file: any) => {
  return new Promise((resolve: any) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const json = XLSX.utils.sheet_to_json(worksheet);
      resolve(json);
    };
    reader.readAsArrayBuffer(file.raw);
  });
};
