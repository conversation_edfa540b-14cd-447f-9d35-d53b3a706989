import request from '@/plugins/axios';

// 获取所有节点列表
export function getAllNodes() {
  return request({
    url: '/waterModels/water/age/nodes',
    method: 'get',
  });
  // 返回数据示例
  // [
  //   {
  //     "id": 1,
  //     "name": "主水源-加压站",
  //     "source": true,
  //     "srcAge": 0,
  //     "age": 0,
  //     "volume": 500,
  //     "pressure": 360000,
  //     "waveSpeed": 1200,
  //     "minPressure": 50000,
  //     "hasAirChamber": true,
  //     "connectedPipes": [],
  //     "h": 36.69724770642202,
  //     "q": null
  //   },
  //   ...
  // ]
}

// 获取所有管线列表
export function getAllPipes() {
  return request({
    url: '/waterModels/water/age/pipes',
    method: 'get',
  });
  // 返回数据示例
  // [
  //   {
  //     "id": 101,
  //     "name": "主输水管1",
  //     "upNodeId": 1,
  //     "downNodeId": 10,
  //     "length": 2000,
  //     "diameter": 0.8,
  //     "flow": 0.25,
  //     "wallThickness": 0.012,
  //     "elasticModulus": ************,
  //     "frictionFactor": 0.018,
  //     "fluidBulkModulus": 2200000000,
  //     "waveSpeed": 1200,
  //     "valveKv": 0,
  //     "valvePosition": 1,
  //     "designPressure": 1200000,
  //     "material": "DI",
  //     "roughness": 0.00026,
  //     "installationType": "buried",
  //     "upNode": null,
  //     "downNode": null
  //   },
  //   ...
  // ]
}

// 获取所有阀门列表
export function getAllValves() {
  return request({
    url: '/waterModels/water/age/valves',
    method: 'get',
  });
  // 返回数据示例
  // [
  // {
  //   "id": 1001,
  //   "pipeId": 101,
  //   "kv": 0.3,
  //   "position": 1,
  //   "closeTime": 0
  // },
  //   ...
  // ]
}

// 标记水龄异常节点
export function waterAgeAbnormalNodes(params?: { threshold: number }) {
  return request({
    url: '/waterModels/water/age-analysis/abnormal-nodes',
    method: 'get',
    params,
  });
}

// 运行水龄仿真
export function runWaterAge(params: { dt: number; totalTime: number }) {
  return request({
    url: '/waterModels/water/age/run',
    method: 'post',
    data: params,
  });
  // 返回数据示例
  //   {
  //   "nodeAges": [
  //     {
  //       "nodeId": 1,
  //       "age": 0
  //     },
  //     {
  //       "nodeId": 2,
  //       "age": 0
  //     },
  //   ],
  //   "pipeAges": [
  //     {
  //       "pipeId": 101,
  //       "outAge": 0
  //     },
  //     {
  //       "pipeId": 102,
  //       "outAge": 0
  //     },
  //   ],
  //   "maxAge": 0,
  //   "averageAge": 0
  // }
}

// 配置水龄分级区间
export function setWaterAge(
  params: [
    {
      gradeName: string;
      minAge: number;
      maxAge: number;
      color: string;
      description: string;
      abnormal: boolean;
    },
  ],
) {
  return request({
    url: '/waterModels/water/age-analysis/grade-config',
    method: 'post',
    data: params,
  });
  // 返回数据示例
  // [
  //   {
  //     "gradeName": "良好",
  //     "minAge": 12,
  //     "maxAge": 24,
  //     "color": "#90EE90",
  //     "description": "水质良好，正常范围",
  //     "abnormal": false
  //   }
  // ];
}

// 获取默认水龄分级配置
export function getDefaultWaterAge() {
  return request({
    url: '/waterModels/water/age-analysis/grade-config/default',
    method: 'get',
  });
  // 返回数据示例
  // [
  //   {
  //     "gradeName": "良好",
  //     "minAge": 12,
  //     "maxAge": 24,
  //     "color": "#90EE90",
  //     "description": "水质良好，正常范围",
  //     "abnormal": false
  //   }
  // ];
}

// 生成水龄分布热力图
export function generateWaterAgeHeatmap(params: {
  analysisTime: string;
  gradeConfigs: [
    {
      gradeName: string;
      minAge: number;
      maxAge: number;
      color: string;
      description: string;
      abnormal: boolean;
    },
  ];
  abnormalThreshold: number;
  includeHeatmap: boolean;
  includeStatistics: boolean;
  markAbnormalNodes: boolean;
  regionGroupField: 'region';
  generateReport: boolean;
  exportFormat: 'PDF';
}) {
  return request({
    url: '/waterModels/water/age-analysis/heatmap',
    method: 'post',
    params,
  });
  // 返回数据示例
  // {
  //   "nodeHeatmapPoints": [
  //   {
  //     "nodeId": 1,
  //     "nodeName": "主水源-加压站",
  //     "longitude": null,
  //     "latitude": null,
  //     "ageValue": 0,
  //     "ageGrade": "良好",
  //     "color": "#90EE90"
  //   },
  //   {
  //     "nodeId": 2,
  //     "nodeName": "次水源-水塔",
  //     "longitude": null,
  //     "latitude": null,
  //     "ageValue": 0,
  //     "ageGrade": "良好",
  //     "color": "#90EE90"
  //   },
  // ],
  // "gradeConfigs": [
  //     {
  //       "gradeName": "良好",
  //       "minAge": 12,
  //       "maxAge": 24,
  //       "color": "#90EE90",
  //       "description": "水质良好，正常范围",
  //       "abnormal": false
  //     }
  //   ],
  //   "colorScale": {
  //     "minValue": 0,
  //     "maxValue": 48,
  //     "colors": [
  //       "#90EE90"
  //     ]
  //   }
  // }
}

// 查询水龄历史数据
export function queryWaterAgeHistory(params?: {
  startTime: string;
  endTime: string;
  nodeIds: number[];
  pipeIds: number[];
  ageThreshold: number;
}) {
  return request({
    url: '/waterModels/water/age-analysis/history/query',
    method: 'post',
    params,
  });
}

// 快速分析
export function quickAnalyzeWaterAge(params: {
  analysisTime: string;
  gradeConfigs: [
    {
      gradeName: string;
      minAge: number;
      maxAge: number;
      color: string;
      description: string;
      abnormal: boolean;
    },
  ];
  abnormalThreshold: number;
  includeHeatmap: boolean;
  includeStatistics: boolean;
  markAbnormalNodes: boolean;
  regionGroupField: 'region';
  generateReport: boolean;
  exportFormat: 'PDF';
}) {
  return request({
    url: '/waterModels/water/age-analysis/quick-analysis',
    method: 'post',
    data: params,
  });
  // 返回数据示例
  // {
  // "statistics": {
  //   "totalNodes": 15,
  //   "averageAge": 0,
  //   "maxAge": 0,
  //   "minAge": 0,
  //   "medianAge": 0,
  //   "standardDeviation": 0,
  //   "intervalStatistics": [
  //     {
  //       "intervalName": "良好",
  //       "minAge": 12,
  //       "maxAge": 24,
  //       "nodeCount": 0,
  //       "percentage": 0,
  //       "nodeIds": []
  //     }
  //   ],
  //   "abnormalNodes": [],
  //   "regionStatistics": [
  //     {
  //       "regionName": "住宅区",
  //       "nodeCount": 2,
  //       "averageAge": 0,
  //       "maxAge": 0,
  //       "minAge": 0,
  //       "abnormalNodeCount": 0
  //     },
  //   ]
  // },
  // "heatmap": {
  //   "nodeHeatmapPoints": [
  //     {
  //       "nodeId": 35,
  //       "nodeName": "医院用户",
  //       "longitude": null,
  //       "latitude": null,
  //       "ageValue": 0,
  //       "ageGrade": "良好",
  //       "color": "#90EE90"
  //     },
  //   ],
  //    "pipeHeatmapPoints": [
  //     {
  //       "pipeId": 101,
  //       "pipeName": "主输水管1",
  //       "upNodeId": 1,
  //       "downNodeId": 10,
  //       "outAgeValue": 0,
  //       "ageGrade": "良好",
  //       "color": "#90EE90"
  //     },
  //   ]
  //   "gradeConfigs": [
  //     {
  //       "gradeName": "良好",
  //       "minAge": 12,
  //       "maxAge": 24,
  //       "color": "#90EE90",
  //       "description": "水质良好，正常范围",
  //       "abnormal": false
  //     }
  //   ],
  //   "colorScale": {
  //     "minValue": 0,
  //     "maxValue": 48,
  //     "colors": [
  //       "#90EE90"
  //     ]
  //   }
  // }
}

// 区域水龄平均值统计对比
export function regionWaterAgeComparison(params: { regionGroupField: 'region' }) {
  return request({
    url: '/waterModels/water/age-analysis/region-comparison',
    method: 'get',
    data: params,
  });
  // 返回数据示例
  // [
  //   {
  //     "regionName": "住宅区",
  //     "nodeCount": 2,
  //     "averageAge": 0,
  //     "maxAge": 0,
  //     "minAge": 0,
  //     "abnormalNodeCount": 0
  //   },
  // ]
}

// 导出水龄分析报告
export function exportWaterAgeReport(params: {
  analysisTime: string;
  gradeConfigs: [
    {
      gradeName: string;
      minAge: number;
      maxAge: number;
      color: string;
      description: string;
      abnormal: boolean;
    },
  ];
  abnormalThreshold: number;
  includeHeatmap: boolean;
  includeStatistics: boolean;
  markAbnormalNodes: boolean;
  regionGroupField: 'region';
  generateReport: boolean;
  exportFormat: 'PDF';
}) {
  return request({
    url: '/waterModels/water/age-analysis/report/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 计算水龄统计数据
export function calculateWaterAgeStatistics(params: {
  analysisTime: string;
  gradeConfigs: [
    {
      gradeName: string;
      minAge: number;
      maxAge: number;
      color: string;
      description: string;
      abnormal: boolean;
    },
  ];
  abnormalThreshold: number;
  includeHeatmap: boolean;
  includeStatistics: boolean;
  markAbnormalNodes: boolean;
  regionGroupField: 'region';
  generateReport: boolean;
  exportFormat: 'PDF';
}) {
  return request({
    url: '/waterModels/water/age-analysis/statistics',
    method: 'post',
    data: params,
  });
  // 返回数据示例
  // {
  //   "totalNodes": 15,
  //   "averageAge": 0,
  //   "maxAge": 0,
  //   "minAge": 0,
  //   "medianAge": 0,
  //   "standardDeviation": 0,
  //   "intervalStatistics": [
  //     {
  //       "intervalName": "良好",
  //       "minAge": 12,
  //       "maxAge": 24,
  //       "nodeCount": 0,
  //       "percentage": 0,
  //       "nodeIds": []
  //     }
  //   ],
  //   "abnormalNodes": [],
  //   "regionStatistics": [
  //     {
  //       "regionName": "住宅区",
  //       "nodeCount": 2,
  //       "averageAge": 0,
  //       "maxAge": 0,
  //       "minAge": 0,
  //       "abnormalNodeCount": 0
  //     },
  //     {
  //       "regionName": "主管网",
  //       "nodeCount": 4,
  //       "averageAge": 0,
  //       "maxAge": 0,
  //       "minAge": 0,
  //       "abnormalNodeCount": 0
  //     },
  //   ]
  // }
}
